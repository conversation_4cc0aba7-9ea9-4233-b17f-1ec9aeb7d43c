{"test_summary": {"total_tests": 13, "passed_tests": 2, "failed_tests": 11, "success_rate": 15.384615384615385, "total_execution_time": 16.274429321289062, "avg_execution_time": 1.251879178560697}, "test_results": [{"test_name": "健康检查接口测试", "status": "FAIL", "error": "Expecting value: line 1 column 1 (char 0)", "execution_time": 0.3729557991027832, "timestamp": "2025-09-04T16:42:54.570900"}, {"test_name": "用户注册功能测试", "status": "FAIL", "error": "Expecting value: line 1 column 1 (char 0)", "execution_time": 0.29998254776000977, "timestamp": "2025-09-04T16:42:55.385612"}, {"test_name": "用户登录功能测试", "status": "FAIL", "error": "Expecting value: line 1 column 1 (char 0)", "execution_time": 0.3173844814300537, "timestamp": "2025-09-04T16:42:56.217302"}, {"test_name": "令牌管理功能测试", "status": "FAIL", "error": "Expecting value: line 1 column 1 (char 0)", "execution_time": 0.2759692668914795, "timestamp": "2025-09-04T16:42:57.009798"}, {"test_name": "OAuth2流程测试", "status": "FAIL", "error": "Expecting value: line 1 column 1 (char 0)", "execution_time": 0.32003068923950195, "timestamp": "2025-09-04T16:42:57.837850"}, {"test_name": "用户登出功能测试", "status": "FAIL", "login_status": 502, "logout_status": 502, "me_after_logout_status": 502, "execution_time": 0.9107310771942139, "timestamp": "2025-09-04T16:42:59.258972"}, {"test_name": "会话管理测试", "status": "FAIL", "login_status": 502, "me_status": 502, "refresh_status": 502, "logout_status": 502, "me_after_logout_status": 502, "execution_time": 1.4524240493774414, "timestamp": "2025-09-04T16:43:01.225331"}, {"test_name": "错误处理机制测试", "status": "FAIL", "invalid_json_status": 502, "missing_field_status": 502, "invalid_method_status": 502, "not_found_status": 502, "execution_time": 1.0707736015319824, "timestamp": "2025-09-04T16:43:02.814748"}, {"test_name": "安全防护措施测试", "status": "PASS", "sql_injection_status": 502, "xss_status": 502, "brute_force_results": [502, 502, 502, 502, 502], "cors_status": 502, "execution_time": 2.1405162811279297, "timestamp": "2025-09-04T16:43:05.473515"}, {"test_name": "密码重置功能测试", "status": "FAIL", "reset_request_status": 502, "reset_confirm_status": 502, "note": "密码重置功能可能未实现", "execution_time": 0.579932689666748, "timestamp": "2025-09-04T16:43:06.558142"}, {"test_name": "账户锁定机制测试", "status": "FAIL", "failed_login_attempts": [502, 502, 502, 502, 502, 502], "correct_login_status": 502, "note": "账户锁定机制可能未实现", "execution_time": 2.150791645050049, "timestamp": "2025-09-04T16:43:09.212424"}, {"test_name": "多因素认证测试", "status": "FAIL", "mfa_setup_status": 502, "mfa_verify_status": 502, "note": "多因素认证功能可能未实现", "execution_time": 0.6843008995056152, "timestamp": "2025-09-04T16:43:10.406544"}, {"test_name": "性能要求测试", "status": "PASS", "avg_registration_time": 0.25518455505371096, "max_registration_time": 0.3043246269226074, "avg_login_time": 0.2714890718460083, "max_login_time": 0.3229501247406006, "concurrent_20_logins_time": 0.38571596145629883, "execution_time": 5.698636293411255, "timestamp": "2025-09-04T16:43:16.608369"}], "performance_metrics": {}, "generated_at": "2025-09-04T16:43:16.615054", "test_environment": {"base_url": "http://localhost:8000", "test_users_count": 3}}