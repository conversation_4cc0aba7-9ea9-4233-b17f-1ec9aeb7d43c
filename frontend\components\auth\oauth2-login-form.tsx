'use client'

import React, { useState } from 'react'
import { useOAuth2Auth } from '@/lib/auth/oauth2-context'
import type { OAuth2LoginCredentials } from '@/lib/types/auth'

interface OAuth2LoginFormProps {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function OAuth2LoginForm({ onSuccess, onError }: OAuth2LoginFormProps) {
  const { loginWithOAuth2, isLoading } = useOAuth2Auth()
  const [credentials, setCredentials] = useState<OAuth2LoginCredentials>({
    username: '',
    password: ''
  })
  const [error, setError] = useState<string>('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!credentials.username || !credentials.password) {
      const errorMsg = '请填写用户名和密码'
      setError(errorMsg)
      onError?.(errorMsg)
      return
    }

    try {
      await loginWithOAuth2(credentials)
      onSuccess?.()
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'OAuth2登录失败'
      setError(errorMsg)
      onError?.(errorMsg)
    }
  }

  const handleInputChange = (field: keyof OAuth2LoginCredentials) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCredentials(prev => ({
      ...prev,
      [field]: e.target.value
    }))
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-2">
            OAuth2 登录
          </h2>
          <p className="text-sm text-gray-600 text-center">
            使用OAuth2密码流进行身份验证
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label 
              htmlFor="username" 
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              用户名/手机号
            </label>
            <input
              id="username"
              type="text"
              value={credentials.username}
              onChange={handleInputChange('username')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入手机号或邮箱"
              disabled={isLoading}
              required
            />
          </div>

          <div>
            <label 
              htmlFor="password" 
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              密码
            </label>
            <input
              id="password"
              type="password"
              value={credentials.password}
              onChange={handleInputChange('password')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入密码"
              disabled={isLoading}
              required
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white font-medium py-2 px-4 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                登录中...
              </span>
            ) : (
              'OAuth2 登录'
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            支持手机号或邮箱登录
          </p>
        </div>
      </div>
    </div>
  )
}
