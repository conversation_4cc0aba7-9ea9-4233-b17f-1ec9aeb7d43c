'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { authService } from '@/lib/api/auth'
import { TokenManager } from '@/lib/auth/token-manager'
import { getErrorMessage } from '@/lib/utils/error-messages'
import type { 
  User, 
  LoginCredentials, 
  RegisterCredentials, 
  OAuth2LoginCredentials,
  OAuth2TokenResponse 
} from '@/lib/types/auth'

interface UnifiedAuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  authMode: 'cookie' | 'bearer' | 'hybrid'
  
  // 认证方法
  login: (credentials: LoginCredentials) => Promise<void>
  loginWithOAuth2: (credentials: OAuth2LoginCredentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
  
  // 令牌管理
  getAccessToken: () => string | null
  hasValidToken: () => boolean
  forceRefresh: () => Promise<boolean>
}

const UnifiedAuthContext = createContext<UnifiedAuthContextType | undefined>(undefined)

export function UnifiedAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [authMode, setAuthMode] = useState<'cookie' | 'bearer' | 'hybrid'>('hybrid')

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      setIsLoading(true)
      
      // 优先尝试Bearer Token认证
      const accessToken = TokenManager.getAccessToken()
      if (accessToken && !TokenManager.isAccessTokenExpired()) {
        try {
          const response = await authService.getCurrentUserWithBearer(accessToken)
          if (response.code === 0) {
            setUser(response.data)
            setAuthMode('bearer')
            return
          }
        } catch (error) {
          console.warn('Bearer token auth failed, trying cookie auth:', error)
          // 不要立即清除令牌，可能是临时网络问题
        }
      }
      
      // 回退到Cookie认证
      try {
        const response = await authService.getCurrentUser()
        if (response.code === 0) {
          setUser(response.data)
          setAuthMode('cookie')
          return
        }
      } catch (error) {
        console.log('Cookie auth also failed, user not authenticated:', error)
      }
      
      // 如果两种认证方式都失败，清除可能无效的令牌
      TokenManager.clearTokens()
      setUser(null)
      setAuthMode('hybrid')
    } catch (error) {
      console.error('Auth check failed:', error)
      setUser(null)
      setAuthMode('hybrid')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true)
    try {
      const response = await authService.login(credentials)
      if (response.code === 0) {
        setUser(response.data)
        setAuthMode('cookie')
      } else {
        throw new Error(getErrorMessage(response.msg) || '登录失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const loginWithOAuth2 = async (credentials: OAuth2LoginCredentials) => {
    setIsLoading(true)
    try {
      const tokenResponse: OAuth2TokenResponse = await authService.loginWithOAuth2(credentials)
      
      // 存储令牌
      TokenManager.storeTokens({
        access_token: tokenResponse.access_token,
        refresh_token: tokenResponse.refresh_token,
        token_type: tokenResponse.token_type,
        expires_in: tokenResponse.expires_in
      })
      
      // 设置用户信息
      setUser(tokenResponse.user)
      setAuthMode('bearer')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'OAuth2登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true)
    try {
      const response = await authService.register(credentials)
      if (response.code === 0) {
        setUser(response.data)
        setAuthMode('cookie')
      } else {
        throw new Error(getErrorMessage(response.msg) || '注册失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除所有认证状态
      TokenManager.clearTokens()
      setUser(null)
      setAuthMode('hybrid')
    }
  }

  const refreshUser = async () => {
    await checkAuth()
  }

  const getAccessToken = () => {
    return TokenManager.getAccessToken()
  }

  const hasValidToken = () => {
    return TokenManager.hasValidAccessToken()
  }

  const forceRefresh = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.code === 0 && data.data) {
          // 更新本地存储的访问令牌
          TokenManager.updateAccessToken(
            data.data.access_token,
            data.data.expires_in
          )
          return true
        }
      }
      return false
    } catch (error) {
      console.error('Force refresh failed:', error)
      return false
    }
  }

  const value: UnifiedAuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    authMode,
    login,
    loginWithOAuth2,
    register,
    logout,
    refreshUser,
    getAccessToken,
    hasValidToken,
    forceRefresh,
  }

  return (
    <UnifiedAuthContext.Provider value={value}>
      {children}
    </UnifiedAuthContext.Provider>
  )
}

export function useUnifiedAuth() {
  const context = useContext(UnifiedAuthContext)
  if (context === undefined) {
    throw new Error('useUnifiedAuth must be used within a UnifiedAuthProvider')
  }
  return context
}