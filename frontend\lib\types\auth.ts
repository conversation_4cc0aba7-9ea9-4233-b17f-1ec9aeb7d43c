export interface User {
  id: number
  phone: string
  user_name: string
  role: 'USER' | 'ADMIN'
  is_active: boolean
}

export interface LoginCredentials {
  phone: string
  password: string
}

export interface RegisterCredentials {
  phone: string
  password: string
}

export interface AuthResponse {
  code: number
  msg: string
  data: User
  tokens: {
    access: string
    refresh: string
  }
}

// OAuth2相关类型
export interface OAuth2TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

export interface OAuth2LoginCredentials {
  username: string  // 可以是手机号或邮箱
  password: string
}

export interface BearerTokenAuth {
  token: string
  type: 'bearer'
}

export interface ApiError {
  code: number
  msg: string
  data: null
}

/**
 * 后端统一的错误码枚举，与后端保持一致
 * 这些值用于前端错误消息映射
 */
export enum ErrorCode {
  OK = "ok",
  INVALID_CREDENTIALS = "invalid_credentials",
  INVALID_PHONE = "invalid_phone",
  INVALID_PASSWORD = "invalid_password",
  PHONE_EXISTS = "phone_exists",
  UNAUTHORIZED = "unauthorized",
  INVALID_TOKEN = "invalid_token",
  USER_NOT_FOUND = "user_not_found",
  INTERNAL_ERROR = "internal_error"
}

export interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}