# 统一认证架构设计

## 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        UI[用户界面]
        AC[统一认证上下文]
        TM[令牌管理器]
        AC_Client[API客户端]
    end
    
    subgraph "后端层"
        BR[路由层]
        BD[认证依赖]
        BS[安全模块]
        AS[认证服务]
        DB[(数据库)]
    end
    
    subgraph "存储层"
        LS[localStorage]
        Cookie[HttpOnly Cookie]
    end
    
    UI --> AC
    AC --> TM
    AC --> AC_Client
    TM --> LS
    TM --> Cookie
    
    AC_Client --> BR
    BR --> BD
    BD --> BS
    BD --> AS
    AS --> DB
    
    BS -.-> Cookie
```

## 认证流程详细图

### 1. 登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 用户界面
    participant AC as 认证上下文
    participant TM as 令牌管理器
    participant AC_Client as API客户端
    participant BR as 后端路由
    participant BD as 认证依赖
    participant AS as 认证服务
    participant DB as 数据库
    
    U->>UI: 输入用户名密码
    UI->>AC: login(credentials)
    AC->>AC_Client: 发送登录请求
    AC_Client->>BR: POST /auth/login
    BR->>BD: 验证请求
    BD->>AS: authenticate_user()
    AS->>DB: 查询用户
    DB-->>AS: 返回用户信息
    AS->>AS: 生成访问令牌和刷新令牌
    AS-->>BD: 返回用户信息和令牌
    BD-->>BR: 返回响应
    BR-->>AC_Client: 返回登录结果
    AC_Client-->>AC: 返回用户信息和令牌
    AC->>TM: storeTokens(tokens)
    TM->>TM: 存储到localStorage
    AC->>AC: 设置用户状态
    AC-->>UI: 登录成功
    UI-->>U: 显示用户信息
```

### 2. 令牌刷新流程

```mermaid
sequenceDiagram
    participant AC_Client as API客户端
    participant BR as 后端路由
    participant BD as 认证依赖
    participant BS as 安全模块
    participant AS as 认证服务
    participant DB as 数据库
    participant TM as 令牌管理器
    
    AC_Client->>BR: 请求受保护资源
    BR-->>AC_Client: 401 Unauthorized
    AC_Client->>AC_Client: 检测到401错误
    AC_Client->>TM: 刷新令牌
    TM->>BR: POST /auth/refresh
    BR->>BD: 验证刷新令牌
    BD->>BS: verify_token()
    BS-->>BD: 令牌验证结果
    BD->>AS: 验证用户状态
    AS->>DB: 查询用户
    DB-->>AS: 返回用户信息
    AS-->>BD: 用户验证结果
    BD->>BS: 生成新的访问令牌
    BS->>BR: 设置新的访问令牌Cookie
    BR-->>TM: 刷新成功
    TM->>TM: 更新本地存储的访问令牌
    TM-->>AC_Client: 刷新完成
    AC_Client->>BR: 重试原始请求
    BR-->>AC_Client: 返回请求结果
```

### 3. 统一认证流程

```mermaid
flowchart TD
    Start[开始请求] --> CheckAuth{检查认证状态}
    
    CheckAuth -->|有Bearer Token| CheckBearer{检查Bearer Token}
    CheckAuth -->|无Bearer Token| CheckCookie{检查Cookie}
    
    CheckBearer -->|有效| SendBearer[发送Bearer Token]
    CheckBearer -->|无效| ClearBearer[清除Bearer Token]
    
    ClearBearer --> CheckCookie
    
    CheckCookie -->|有效| SendCookie[使用Cookie认证]
    CheckCookie -->|无效| Unauthorized[返回401]
    
    SendBearer --> Request[发送请求]
    SendCookie --> Request
    
    Request --> CheckResponse{检查响应}
    
    CheckResponse -->|200 OK| Success[请求成功]
    CheckResponse -->|401 Unauthorized| Refresh{刷新令牌}
    
    Refresh -->|刷新成功| UpdateToken[更新本地令牌]
    Refresh -->|刷新失败| Unauthorized
    
    UpdateToken --> Retry[重试请求]
    Retry --> Request
    
    Success --> End[结束]
    Unauthorized --> End
```

## 组件关系图

### 前端组件关系

```mermaid
graph LR
    subgraph "认证核心"
        AC[AuthContext 统一认证上下文]
        TM[TokenManager 令牌管理器]
    end
    
    subgraph "API层"
        AC_Client[ApiClient]
        Auth_Service[AuthService]
    end
    
    subgraph "UI组件"
        LoginForm[登录表单]
        AuthStatus[认证状态]
        ProtectedRoute[受保护路由]
    end
    
    subgraph "存储"
        LocalStorage[localStorage]
        CookieStorage[Cookie]
    end
    
    AC --> TM
    AC --> Auth_Service
    Auth_Service --> AC_Client
    TM --> LocalStorage
    TM --> CookieStorage
    
    LoginForm --> AC
    AuthStatus --> AC
    ProtectedRoute --> AC
```

### 后端组件关系

```mermaid
graph LR
    subgraph "路由层"
        AuthRoutes[认证路由]
        ProtectedRoutes[受保护路由]
    end
    
    subgraph "认证层"
        AuthDeps[认证依赖]
        Security[安全模块]
    end
    
    subgraph "服务层"
        AuthService[认证服务]
        OAuth2Service[OAuth2服务]
    end
    
    subgraph "数据层"
        UserModels[用户模型]
        Database[数据库]
    end
    
    AuthRoutes --> AuthDeps
    ProtectedRoutes --> AuthDeps
    AuthDeps --> Security
    AuthDeps --> AuthService
    AuthDeps --> OAuth2Service
    AuthService --> UserModels
    OAuth2Service --> UserModels
    UserModels --> Database
```

## 数据流图

### 令牌数据流

```mermaid
flowchart LR
    subgraph "前端"
        UI[用户界面]
        AC[认证上下文]
        TM[令牌管理器]
        API[API客户端]
    end
    
    subgraph "后端"
        Routes[路由]
        Auth[认证服务]
        Security[安全模块]
    end
    
    subgraph "存储"
        LS[localStorage]
        Cookie[HttpOnly Cookie]
    end
    
    UI --> AC
    AC --> TM
    TM --> LS
    TM --> Cookie
    AC --> API
    API --> Routes
    Routes --> Auth
    Auth --> Security
    Security -.-> Cookie
```

## 状态转换图

### 认证状态转换

```mermaid
stateDiagram-v2
    [*] --> Loading: 初始化
    Loading --> Authenticated: 认证成功
    Loading --> Unauthenticated: 认证失败
    Authenticated --> Refreshing: 令牌过期
    Refreshing --> Authenticated: 刷新成功
    Refreshing --> Unauthenticated: 刷新失败
    Authenticated --> Unauthenticated: 登出
    Unauthenticated --> Authenticated: 登录
    Unauthenticated --> [*]: 清理状态
    Authenticated --> [*]: 清理状态
```

## 接口设计

### 统一认证上下文接口

```typescript
interface UnifiedAuthContext {
  // 状态
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  authMode: 'bearer' | 'cookie' | 'hybrid'
  
  // 方法
  login: (credentials: LoginCredentials) => Promise<void>
  loginWithOAuth2: (credentials: OAuth2Credentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
  
  // 令牌管理
  getAccessToken: () => string | null
  hasValidToken: () => boolean
  forceRefresh: () => Promise<boolean>
}
```

### 统一令牌管理器接口

```typescript
interface UnifiedTokenManager {
  // 存储方法
  storeTokens: (tokens: TokenData) => void
  updateAccessToken: (token: string, expiresIn: number) => void
  
  // 获取方法
  getAccessToken: () => string | null
  getRefreshToken: () => string | null
  getTokenData: () => TokenData | null
  
  // 验证方法
  isAccessTokenExpired: () => boolean
  hasValidAccessToken: () => boolean
  shouldUseBearerAuth: () => boolean
  
  // 清理方法
  clearTokens: () => void
  
  // 头部方法
  getAuthorizationHeader: () => string | null
}
```

这个架构设计展示了统一认证系统的各个组件及其关系，包括前端和后端的交互流程、数据流向和状态转换。通过这个设计，我们可以实现一个简洁、可靠且易于维护的认证系统。