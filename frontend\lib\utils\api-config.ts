/**
 * Dynamic API base URL configuration
 * Automatically adapts to the current environment (localhost vs IP address)
 */

/**
 * Get the API base URL dynamically based on the current environment
 * @returns The appropriate API base URL
 */
export function getApiBaseUrl(): string {
  // If we have an explicit environment variable, use it
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL
  }
  
  // For client-side, dynamically determine based on current host
  if (typeof window !== 'undefined') {
    const currentHost = window.location.hostname
    const currentProtocol = window.location.protocol
    
    // If accessing via IP address, use the same IP for API calls
    if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
      return `${currentProtocol}//${currentHost}:8000`
    }
  }
  
  // Default fallback for localhost and server-side rendering
  return 'http://localhost:8000'
}

/**
 * Get the current environment info for debugging
 */
export function getEnvironmentInfo() {
  if (typeof window === 'undefined') {
    return {
      isServer: true,
      hostname: 'server-side',
      protocol: 'unknown',
      apiBaseUrl: getApiBaseUrl()
    }
  }
  
  return {
    isServer: false,
    hostname: window.location.hostname,
    protocol: window.location.protocol,
    apiBaseUrl: getApiBaseUrl()
  }
}
