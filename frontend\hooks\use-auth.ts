'use client'

import React from 'react'
import { useAuth as useAuthContext } from '@/contexts/auth-context'
import type { User } from '@/lib/types/auth'

export function useAuth() {
  const context = useAuthContext()
  
  // Auto-refresh user session periodically
  React.useEffect(() => {
    const refreshInterval = setInterval(() => {
      if (context.isAuthenticated) {
        context.refreshUser()
      }
    }, 15 * 60 * 1000) // Refresh every 15 minutes
    
    return () => clearInterval(refreshInterval)
  }, [context.isAuthenticated, context.refreshUser])
  
  // Handle page visibility changes
  React.useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && context.isAuthenticated) {
        context.refreshUser()
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [context.isAuthenticated, context.refreshUser])
  
  return context
}

export function useAuthenticatedUser(): { user: User | null; isAuthenticated: boolean; isLoading: boolean } {
  const { user, isAuthenticated, isLoading } = useAuth()

  // 不使用条件性返回，而是始终返回相同的结构
  return {
    user: isLoading ? null : (isAuthenticated ? user : null),
    isAuthenticated: !isLoading && isAuthenticated,
    isLoading
  }
}