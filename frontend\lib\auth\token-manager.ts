/**
 * Token管理器
 * 处理Bearer Token的存储、获取和清除
 * 支持localStorage和sessionStorage
 */

export interface TokenData {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  expires_at: number // 计算出的过期时间戳
}

export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'hermes_access_token'
  private static readonly REFRESH_TOKEN_KEY = 'hermes_refresh_token'
  private static readonly TOKEN_DATA_KEY = 'hermes_token_data'

  /**
   * 存储OAuth2令牌数据
   */
  static storeTokens(tokenResponse: {
    access_token: string
    refresh_token: string
    token_type: string
    expires_in: number
  }): void {
    const now = Date.now()
    const tokenData: TokenData = {
      ...tokenResponse,
      expires_at: now + (tokenResponse.expires_in * 1000)
    }

    try {
      // 存储到localStorage
      localStorage.setItem(this.ACCESS_TOKEN_KEY, tokenResponse.access_token)
      localStorage.setItem(this.REFRESH_TOKEN_KEY, tokenResponse.refresh_token)
      localStorage.setItem(this.TOKEN_DATA_KEY, JSON.stringify(tokenData))
    } catch (error) {
      console.warn('Failed to store tokens in localStorage:', error)
    }
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    try {
      return localStorage.getItem(this.ACCESS_TOKEN_KEY)
    } catch (error) {
      console.warn('Failed to get access token from localStorage:', error)
      return null
    }
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    try {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY)
    } catch (error) {
      console.warn('Failed to get refresh token from localStorage:', error)
      return null
    }
  }

  /**
   * 获取完整的令牌数据
   */
  static getTokenData(): TokenData | null {
    try {
      const data = localStorage.getItem(this.TOKEN_DATA_KEY)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn('Failed to get token data from localStorage:', error)
      return null
    }
  }

  /**
   * 检查访问令牌是否过期
   */
  static isAccessTokenExpired(): boolean {
    const tokenData = this.getTokenData()
    if (!tokenData) return true

    const now = Date.now()
    // 提前5分钟认为令牌过期，避免边界情况
    const bufferTime = 5 * 60 * 1000
    return now >= (tokenData.expires_at - bufferTime)
  }

  /**
   * 检查是否有有效的访问令牌
   */
  static hasValidAccessToken(): boolean {
    const token = this.getAccessToken()
    return token !== null && !this.isAccessTokenExpired()
  }

  /**
   * 清除所有令牌
   */
  static clearTokens(): void {
    try {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY)
      localStorage.removeItem(this.REFRESH_TOKEN_KEY)
      localStorage.removeItem(this.TOKEN_DATA_KEY)
    } catch (error) {
      console.warn('Failed to clear tokens from localStorage:', error)
    }
  }

  /**
   * 更新访问令牌（用于刷新令牌场景）
   */
  static updateAccessToken(newAccessToken: string, expiresIn: number): void {
    try {
      const now = Date.now()
      localStorage.setItem(this.ACCESS_TOKEN_KEY, newAccessToken)
      
      // 更新令牌数据
      const tokenData = this.getTokenData()
      if (tokenData) {
        tokenData.access_token = newAccessToken
        tokenData.expires_in = expiresIn
        tokenData.expires_at = now + (expiresIn * 1000)
        localStorage.setItem(this.TOKEN_DATA_KEY, JSON.stringify(tokenData))
      }
    } catch (error) {
      console.warn('Failed to update access token:', error)
    }
  }

  /**
   * 获取Authorization头值
   */
  static getAuthorizationHeader(): string | null {
    const token = this.getAccessToken()
    return token ? `Bearer ${token}` : null
  }

  /**
   * 检查是否应该使用Bearer Token认证
   * 如果有有效的Bearer Token，优先使用Bearer认证
   */
  static shouldUseBearerAuth(): boolean {
    return this.hasValidAccessToken()
  }
}
