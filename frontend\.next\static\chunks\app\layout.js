/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1eee80b42e57\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMWVlZTgwYjQyZTU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/auth */ \"(app-pages-browser)/./lib/api/auth.ts\");\n/* harmony import */ var _lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/error-messages */ \"(app-pages-browser)/./lib/utils/error-messages.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            if (response.code === 0) {\n                setUser(response.data);\n            }\n        } catch (error) {\n            console.log('User not authenticated:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        setIsLoading(true);\n        try {\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.login(credentials);\n            if (response.code === 0) {\n                setUser(response.data);\n            } else {\n                throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(response.msg) || '登录失败');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (credentials)=>{\n        setIsLoading(true);\n        try {\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.register(credentials);\n            if (response.code === 0) {\n                setUser(response.data);\n            } else {\n                throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(response.msg) || '注册失败');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    const refreshUser = async ()=>{\n        await checkAuth();\n    };\n    const value = {\n        user,\n        isAuthenticated: !!user,\n        isLoading,\n        login,\n        register,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 83,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbnRleHRzL2F1dGgtY29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTZFO0FBRWpDO0FBQ2dCO0FBRTVELE1BQU1PLDRCQUFjTixvREFBYUEsQ0FBOEJPO0FBRXhELFNBQVNDLGFBQWEsS0FBMkM7UUFBM0MsRUFBRUMsUUFBUSxFQUFpQyxHQUEzQzs7SUFDM0IsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdSLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1MsV0FBV0MsYUFBYSxHQUFHViwrQ0FBUUEsQ0FBQztJQUUzQ0QsZ0RBQVNBO2tDQUFDO1lBQ1JZO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLE1BQU1BLFlBQVk7UUFDaEIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTVgsc0RBQVdBLENBQUNZLGNBQWM7WUFDakQsSUFBSUQsU0FBU0UsSUFBSSxLQUFLLEdBQUc7Z0JBQ3ZCTixRQUFRSSxTQUFTRyxJQUFJO1lBQ3ZCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJGO1FBQ3pDLFNBQVU7WUFDUk4sYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNUyxRQUFRLE9BQU9DO1FBQ25CVixhQUFhO1FBQ2IsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTVgsc0RBQVdBLENBQUNrQixLQUFLLENBQUNDO1lBQ3pDLElBQUlSLFNBQVNFLElBQUksS0FBSyxHQUFHO2dCQUN2Qk4sUUFBUUksU0FBU0csSUFBSTtZQUN2QixPQUFPO2dCQUNMLE1BQU0sSUFBSU0sTUFBTW5CLDBFQUFlQSxDQUFDVSxTQUFTVSxHQUFHLEtBQUs7WUFDbkQ7UUFDRixTQUFVO1lBQ1JaLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTWEsV0FBVyxPQUFPSDtRQUN0QlYsYUFBYTtRQUNiLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1YLHNEQUFXQSxDQUFDc0IsUUFBUSxDQUFDSDtZQUM1QyxJQUFJUixTQUFTRSxJQUFJLEtBQUssR0FBRztnQkFDdkJOLFFBQVFJLFNBQVNHLElBQUk7WUFDdkIsT0FBTztnQkFDTCxNQUFNLElBQUlNLE1BQU1uQiwwRUFBZUEsQ0FBQ1UsU0FBU1UsR0FBRyxLQUFLO1lBQ25EO1FBQ0YsU0FBVTtZQUNSWixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1jLFNBQVM7UUFDYixJQUFJO1lBQ0YsTUFBTXZCLHNEQUFXQSxDQUFDdUIsTUFBTTtRQUMxQixFQUFFLE9BQU9SLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlCQUFpQkE7UUFDakMsU0FBVTtZQUNSUixRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU1pQixjQUFjO1FBQ2xCLE1BQU1kO0lBQ1I7SUFFQSxNQUFNZSxRQUF5QjtRQUM3Qm5CO1FBQ0FvQixpQkFBaUIsQ0FBQyxDQUFDcEI7UUFDbkJFO1FBQ0FVO1FBQ0FJO1FBQ0FDO1FBQ0FDO0lBQ0Y7SUFFQSxxQkFBTyw4REFBQ3RCLFlBQVl5QixRQUFRO1FBQUNGLE9BQU9BO2tCQUFRcEI7Ozs7OztBQUM5QztHQTFFZ0JEO0tBQUFBO0FBNEVULFNBQVN3Qjs7SUFDZCxNQUFNQyxVQUFVaEMsaURBQVVBLENBQUNLO0lBQzNCLElBQUkyQixZQUFZMUIsV0FBVztRQUN6QixNQUFNLElBQUlpQixNQUFNO0lBQ2xCO0lBQ0EsT0FBT1M7QUFDVDtJQU5nQkQiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxjb250ZXh0c1xcYXV0aC1jb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB0eXBlIHsgVXNlciwgTG9naW5DcmVkZW50aWFscywgUmVnaXN0ZXJDcmVkZW50aWFscywgQXV0aENvbnRleHRUeXBlIH0gZnJvbSAnQC9saWIvdHlwZXMvYXV0aCdcclxuaW1wb3J0IHsgYXV0aFNlcnZpY2UgfSBmcm9tICdAL2xpYi9hcGkvYXV0aCdcclxuaW1wb3J0IHsgZ2V0RXJyb3JNZXNzYWdlIH0gZnJvbSAnQC9saWIvdXRpbHMvZXJyb3ItbWVzc2FnZXMnXHJcblxyXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbClcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNoZWNrQXV0aCgpXHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGNoZWNrQXV0aCA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aFNlcnZpY2UuZ2V0Q3VycmVudFVzZXIoKVxyXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMCkge1xyXG4gICAgICAgIHNldFVzZXIocmVzcG9uc2UuZGF0YSlcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQ6JywgZXJyb3IpXHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBsb2dpbiA9IGFzeW5jIChjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscykgPT4ge1xyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1dGhTZXJ2aWNlLmxvZ2luKGNyZWRlbnRpYWxzKVxyXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMCkge1xyXG4gICAgICAgIHNldFVzZXIocmVzcG9uc2UuZGF0YSlcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZ2V0RXJyb3JNZXNzYWdlKHJlc3BvbnNlLm1zZykgfHwgJ+eZu+W9leWksei0pScpXHJcbiAgICAgIH1cclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IHJlZ2lzdGVyID0gYXN5bmMgKGNyZWRlbnRpYWxzOiBSZWdpc3RlckNyZWRlbnRpYWxzKSA9PiB7XHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXV0aFNlcnZpY2UucmVnaXN0ZXIoY3JlZGVudGlhbHMpXHJcbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAwKSB7XHJcbiAgICAgICAgc2V0VXNlcihyZXNwb25zZS5kYXRhKVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihnZXRFcnJvck1lc3NhZ2UocmVzcG9uc2UubXNnKSB8fCAn5rOo5YaM5aSx6LSlJylcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgYXV0aFNlcnZpY2UubG9nb3V0KClcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ291dCBlcnJvcjonLCBlcnJvcilcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldFVzZXIobnVsbClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IHJlZnJlc2hVc2VyID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgYXdhaXQgY2hlY2tBdXRoKClcclxuICB9XHJcblxyXG4gIGNvbnN0IHZhbHVlOiBBdXRoQ29udGV4dFR5cGUgPSB7XHJcbiAgICB1c2VyLFxyXG4gICAgaXNBdXRoZW50aWNhdGVkOiAhIXVzZXIsXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICBsb2dpbixcclxuICAgIHJlZ2lzdGVyLFxyXG4gICAgbG9nb3V0LFxyXG4gICAgcmVmcmVzaFVzZXIsXHJcbiAgfVxyXG5cclxuICByZXR1cm4gPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+e2NoaWxkcmVufTwvQXV0aENvbnRleHQuUHJvdmlkZXI+XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KVxyXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpXHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0XHJcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiYXV0aFNlcnZpY2UiLCJnZXRFcnJvck1lc3NhZ2UiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJjaGVja0F1dGgiLCJyZXNwb25zZSIsImdldEN1cnJlbnRVc2VyIiwiY29kZSIsImRhdGEiLCJlcnJvciIsImNvbnNvbGUiLCJsb2ciLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwiRXJyb3IiLCJtc2ciLCJyZWdpc3RlciIsImxvZ291dCIsInJlZnJlc2hVc2VyIiwidmFsdWUiLCJpc0F1dGhlbnRpY2F0ZWQiLCJQcm92aWRlciIsInVzZUF1dGgiLCJjb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/auth.ts":
/*!*************************!*\
  !*** ./lib/api/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./lib/api/client.ts\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* harmony import */ var _lib_utils_api_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/api-config */ \"(app-pages-browser)/./lib/utils/api-config.ts\");\n\n\n\nconst authService = {\n    async login (credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/login', credentials);\n        if (response.data) {\n            response.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(response.data.role);\n        }\n        return response;\n    },\n    async register (credentials) {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/register', credentials);\n        if (response.data) {\n            response.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(response.data.role);\n        }\n        return response;\n    },\n    async getCurrentUser () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/auth/me');\n        if (response.data) {\n            response.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(response.data.role);\n        }\n        return response;\n    },\n    async logout () {\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/logout');\n        return response;\n    },\n    // OAuth2相关方法\n    async loginWithOAuth2 (credentials) {\n        // OAuth2密码流需要使用form data\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n        const response = await fetch(\"\".concat((0,_lib_utils_api_config__WEBPACK_IMPORTED_MODULE_2__.getApiBaseUrl)(), \"/auth/token\"), {\n            method: 'POST',\n            body: formData,\n            credentials: 'include'\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"OAuth2 login failed: \".concat(response.status));\n        }\n        const tokenData = await response.json();\n        // 标准化用户角色\n        if (tokenData.user) {\n            tokenData.user.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(tokenData.user.role);\n        }\n        return tokenData;\n    },\n    async getCurrentUserWithBearer (token) {\n        const response = await fetch(\"\".concat((0,_lib_utils_api_config__WEBPACK_IMPORTED_MODULE_2__.getApiBaseUrl)(), \"/auth/me\"), {\n            method: 'GET',\n            headers: {\n                'Authorization': \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            },\n            credentials: 'include'\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.detail || \"Bearer auth failed: \".concat(response.status));\n        }\n        const userData = await response.json();\n        // 标准化用户角色\n        if (userData.data) {\n            userData.data.role = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_1__.normalizeRole)(userData.data.role);\n        }\n        return userData;\n    },\n    // 混合认证方法（优先Bearer Token，然后Cookie）\n    async getCurrentUserFlexible (bearerToken) {\n        if (bearerToken) {\n            try {\n                return await this.getCurrentUserWithBearer(bearerToken);\n            } catch (error) {\n                console.warn('Bearer token auth failed, falling back to cookie auth:', error);\n            }\n        }\n        // 回退到Cookie认证\n        return await this.getCurrentUser();\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/client.ts":
/*!***************************!*\
  !*** ./lib/api/client.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/error-messages */ \"(app-pages-browser)/./lib/utils/error-messages.ts\");\n/* harmony import */ var _lib_auth_token_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth/token-manager */ \"(app-pages-browser)/./lib/auth/token-manager.ts\");\n/* harmony import */ var _lib_utils_api_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/api-config */ \"(app-pages-browser)/./lib/utils/api-config.ts\");\n// Note: This implementation uses native fetch instead of axios to avoid dependency issues\n// You can replace this with axios later if needed\n\n\n\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, _retry = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        // 准备请求头\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        // 合并用户提供的headers\n        if (options.headers) {\n            Object.assign(headers, options.headers);\n        }\n        // 优先使用Bearer Token认证\n        if (_lib_auth_token_manager__WEBPACK_IMPORTED_MODULE_1__.TokenManager.shouldUseBearerAuth()) {\n            const authHeader = _lib_auth_token_manager__WEBPACK_IMPORTED_MODULE_1__.TokenManager.getAuthorizationHeader();\n            if (authHeader) {\n                headers['Authorization'] = authHeader;\n            }\n        }\n        const config = {\n            headers,\n            credentials: 'include',\n            ...options\n        };\n        try {\n            const response = await fetch(url, config);\n            // 优先尝试解析JSON（无论状态码）\n            let result = null;\n            try {\n                result = await response.json();\n            } catch (e) {\n            // ignore parse error; result 可能为 null\n            }\n            // 非200状态码处理（包含HTTP 401）\n            if (!response.ok) {\n                if (response.status === 401 && _retry && endpoint !== '/auth/refresh') {\n                    const refreshed = await this.refreshAccessToken();\n                    if (refreshed) {\n                        return this.request(endpoint, options, false);\n                    }\n                    // 对于 /auth/me 端点的401，不进行重定向，而是抛出正常错误\n                    // 这样前端可以优雅地处理未登录状态\n                    if (endpoint !== '/auth/me' && \"object\" !== 'undefined') {\n                        window.location.href = '/';\n                    }\n                }\n                if (result && result.msg) {\n                    throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(result.msg) || \"HTTP error! status: \".concat(response.status));\n                }\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            // 业务包装：即使是200响应，也需要检查code\n            if (result && typeof result.code !== 'undefined') {\n                if (result.code === 0) {\n                    return result;\n                }\n                if (result.code === 401 && _retry && endpoint !== '/auth/refresh') {\n                    const refreshed = await this.refreshAccessToken();\n                    if (refreshed) {\n                        return this.request(endpoint, options, false);\n                    }\n                    // 对于 /auth/me 端点的401，不进行重定向，而是抛出正常错误\n                    // 这样前端可以优雅地处理未登录状态\n                    if (endpoint !== '/auth/me' && \"object\" !== 'undefined') {\n                        window.location.href = '/';\n                    }\n                    throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(result.msg) || '请先登录');\n                }\n                throw new Error((0,_lib_utils_error_messages__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(result.msg) || '请求失败');\n            }\n            // 无包装时，直接返回解析结果\n            return result;\n        } catch (error) {\n            if (error instanceof Error) {\n                // 只对非 /auth/me 端点的认证错误进行重定向\n                if ((error.message.includes('登录已过期') || error.message.includes('请先登录') || error.message.includes('401') || error.message.includes('Unauthorized')) && endpoint !== '/auth/me') {\n                    // Token expired, redirect to login page\n                    if (true) {\n                        window.location.href = '/';\n                    }\n                }\n                throw error;\n            }\n            throw new Error('Network error occurred');\n        }\n    }\n    async get(endpoint) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return this.request(endpoint, {\n            ...config,\n            method: 'GET'\n        });\n    }\n    async post(endpoint) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return this.request(endpoint, {\n            ...config,\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async put(endpoint) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return this.request(endpoint, {\n            ...config,\n            method: 'PUT',\n            body: JSON.stringify(data)\n        });\n    }\n    async delete(endpoint) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return this.request(endpoint, {\n            ...config,\n            method: 'DELETE'\n        });\n    }\n    // 静默刷新：仅调用一次，成功则更新 httpOnly cookie_access\n    async refreshAccessToken() {\n        try {\n            const url = \"\".concat((0,_lib_utils_api_config__WEBPACK_IMPORTED_MODULE_2__.getApiBaseUrl)(), \"/auth/refresh\");\n            const resp = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include'\n            });\n            let data = null;\n            try {\n                data = await resp.json();\n            } catch (e) {\n            // ignore parse error\n            }\n            return !!(resp.ok && data && data.code === 0);\n        } catch (e) {\n            return false;\n        }\n    }\n    constructor(){\n        this.baseURL = (0,_lib_utils_api_config__WEBPACK_IMPORTED_MODULE_2__.getApiBaseUrl)();\n    }\n}\nconst apiClient = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth/token-manager.ts":
/*!***********************************!*\
  !*** ./lib/auth/token-manager.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager)\n/* harmony export */ });\n/**\n * Token管理器\n * 处理Bearer Token的存储、获取和清除\n * 支持localStorage和sessionStorage\n */ class TokenManager {\n    /**\n   * 存储OAuth2令牌数据\n   */ static storeTokens(tokenResponse) {\n        const now = Date.now();\n        const tokenData = {\n            ...tokenResponse,\n            expires_at: now + tokenResponse.expires_in * 1000\n        };\n        try {\n            // 存储到localStorage\n            localStorage.setItem(this.ACCESS_TOKEN_KEY, tokenResponse.access_token);\n            localStorage.setItem(this.REFRESH_TOKEN_KEY, tokenResponse.refresh_token);\n            localStorage.setItem(this.TOKEN_DATA_KEY, JSON.stringify(tokenData));\n        } catch (error) {\n            console.warn('Failed to store tokens in localStorage:', error);\n        }\n    }\n    /**\n   * 获取访问令牌\n   */ static getAccessToken() {\n        try {\n            return localStorage.getItem(this.ACCESS_TOKEN_KEY);\n        } catch (error) {\n            console.warn('Failed to get access token from localStorage:', error);\n            return null;\n        }\n    }\n    /**\n   * 获取刷新令牌\n   */ static getRefreshToken() {\n        try {\n            return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n        } catch (error) {\n            console.warn('Failed to get refresh token from localStorage:', error);\n            return null;\n        }\n    }\n    /**\n   * 获取完整的令牌数据\n   */ static getTokenData() {\n        try {\n            const data = localStorage.getItem(this.TOKEN_DATA_KEY);\n            return data ? JSON.parse(data) : null;\n        } catch (error) {\n            console.warn('Failed to get token data from localStorage:', error);\n            return null;\n        }\n    }\n    /**\n   * 检查访问令牌是否过期\n   */ static isAccessTokenExpired() {\n        const tokenData = this.getTokenData();\n        if (!tokenData) return true;\n        const now = Date.now();\n        // 提前5分钟认为令牌过期，避免边界情况\n        const bufferTime = 5 * 60 * 1000;\n        return now >= tokenData.expires_at - bufferTime;\n    }\n    /**\n   * 检查是否有有效的访问令牌\n   */ static hasValidAccessToken() {\n        const token = this.getAccessToken();\n        return token !== null && !this.isAccessTokenExpired();\n    }\n    /**\n   * 清除所有令牌\n   */ static clearTokens() {\n        try {\n            localStorage.removeItem(this.ACCESS_TOKEN_KEY);\n            localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n            localStorage.removeItem(this.TOKEN_DATA_KEY);\n        } catch (error) {\n            console.warn('Failed to clear tokens from localStorage:', error);\n        }\n    }\n    /**\n   * 更新访问令牌（用于刷新令牌场景）\n   */ static updateAccessToken(newAccessToken, expiresIn) {\n        try {\n            const now = Date.now();\n            localStorage.setItem(this.ACCESS_TOKEN_KEY, newAccessToken);\n            // 更新令牌数据\n            const tokenData = this.getTokenData();\n            if (tokenData) {\n                tokenData.access_token = newAccessToken;\n                tokenData.expires_in = expiresIn;\n                tokenData.expires_at = now + expiresIn * 1000;\n                localStorage.setItem(this.TOKEN_DATA_KEY, JSON.stringify(tokenData));\n            }\n        } catch (error) {\n            console.warn('Failed to update access token:', error);\n        }\n    }\n    /**\n   * 获取Authorization头值\n   */ static getAuthorizationHeader() {\n        const token = this.getAccessToken();\n        return token ? \"Bearer \".concat(token) : null;\n    }\n    /**\n   * 检查是否应该使用Bearer Token认证\n   * 如果有有效的Bearer Token，优先使用Bearer认证\n   */ static shouldUseBearerAuth() {\n        return this.hasValidAccessToken();\n    }\n}\nTokenManager.ACCESS_TOKEN_KEY = 'hermes_access_token';\nTokenManager.REFRESH_TOKEN_KEY = 'hermes_refresh_token';\nTokenManager.TOKEN_DATA_KEY = 'hermes_token_data';\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth/token-manager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils/api-config.ts":
/*!*********************************!*\
  !*** ./lib/utils/api-config.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getApiBaseUrl: () => (/* binding */ getApiBaseUrl),\n/* harmony export */   getEnvironmentInfo: () => (/* binding */ getEnvironmentInfo)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Dynamic API base URL configuration\n * Automatically adapts to the current environment (localhost vs IP address)\n */ /**\n * Get the API base URL dynamically based on the current environment\n * @returns The appropriate API base URL\n */ function getApiBaseUrl() {\n    // If we have an explicit environment variable, use it\n    if (process.env.NEXT_PUBLIC_API_URL) {\n        return process.env.NEXT_PUBLIC_API_URL;\n    }\n    // For client-side, dynamically determine based on current host\n    if (true) {\n        const currentHost = window.location.hostname;\n        const currentProtocol = window.location.protocol;\n        // If accessing via IP address, use the same IP for API calls\n        if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {\n            return \"\".concat(currentProtocol, \"//\").concat(currentHost, \":8000\");\n        }\n    }\n    // Default fallback for localhost and server-side rendering\n    return 'http://localhost:8000';\n}\n/**\n * Get the current environment info for debugging\n */ function getEnvironmentInfo() {\n    if (false) {}\n    return {\n        isServer: false,\n        hostname: window.location.hostname,\n        protocol: window.location.protocol,\n        apiBaseUrl: getApiBaseUrl()\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/api-config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils/error-messages.ts":
/*!*************************************!*\
  !*** ./lib/utils/error-messages.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage)\n/* harmony export */ });\n/**\r\n * 错误码与用户友好消息的映射\r\n * \r\n * 这个文件定义了后端API返回的错误码与前端展示的用户友好消息之间的映射关系。\r\n * 后端使用统一的错误码枚举（如ok, invalid_credentials等），前端将其转换为可读性更好的错误消息。\r\n */ /**\r\n * 错误码与用户友好消息的映射对象\r\n */ const ERROR_MESSAGES = {\n    ok: '操作成功',\n    invalid_credentials: '账号或密码错误，请重试',\n    invalid_phone: '手机号格式不正确',\n    invalid_password: '密码格式不正确，密码长度应为8-64位',\n    phone_exists: '该手机号已被注册',\n    unauthorized: '请先登录后再操作',\n    invalid_token: '登录已过期，请重新登录',\n    user_not_found: '用户不存在',\n    internal_error: '服务器内部错误，请稍后再试'\n};\n/**\r\n * 根据错误码获取用户友好的错误消息\r\n * \r\n * @param code 错误码\r\n * @returns 用户友好的错误消息\r\n */ function getErrorMessage(code) {\n    if (!code) return ERROR_MESSAGES.internal_error;\n    return code in ERROR_MESSAGES ? ERROR_MESSAGES[code] : \"未知错误 (\".concat(code, \")\");\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi91dGlscy9lcnJvci1tZXNzYWdlcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7OztDQUtDLEdBYUQ7O0NBRUMsR0FDTSxNQUFNQSxpQkFBNEM7SUFDdkRDLElBQUk7SUFDSkMscUJBQXFCO0lBQ3JCQyxlQUFlO0lBQ2ZDLGtCQUFrQjtJQUNsQkMsY0FBYztJQUNkQyxjQUFjO0lBQ2RDLGVBQWU7SUFDZkMsZ0JBQWdCO0lBQ2hCQyxnQkFBZ0I7QUFDbEIsRUFBRTtBQUVGOzs7OztDQUtDLEdBQ00sU0FBU0MsZ0JBQWdCQyxJQUF3QjtJQUN0RCxJQUFJLENBQUNBLE1BQU0sT0FBT1gsZUFBZVMsY0FBYztJQUUvQyxPQUFPLFFBQVNULGlCQUNaQSxjQUFjLENBQUNXLEtBQWtCLEdBQ2pDLFNBQWMsT0FBTEEsTUFBSztBQUNwQiIsInNvdXJjZXMiOlsiRDpcXG15X3Byb2plY3RcXHdvcmtcXGhlcm1lc1xcZnJvbnRlbmRcXGxpYlxcdXRpbHNcXGVycm9yLW1lc3NhZ2VzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiDplJnor6/noIHkuI7nlKjmiLflj4vlpb3mtojmga/nmoTmmKDlsIRcclxuICogXHJcbiAqIOi/meS4quaWh+S7tuWumuS5ieS6huWQjuerr0FQSei/lOWbnueahOmUmeivr+eggeS4juWJjeerr+WxleekuueahOeUqOaIt+WPi+Wlvea2iOaBr+S5i+mXtOeahOaYoOWwhOWFs+ezu+OAglxyXG4gKiDlkI7nq6/kvb/nlKjnu5/kuIDnmoTplJnor6/noIHmnprkuL7vvIjlpoJvaywgaW52YWxpZF9jcmVkZW50aWFsc+etie+8ie+8jOWJjeerr+WwhuWFtui9rOaNouS4uuWPr+ivu+aAp+abtOWlveeahOmUmeivr+a2iOaBr+OAglxyXG4gKi9cclxuXHJcbmV4cG9ydCB0eXBlIEVycm9yQ29kZSA9XHJcbiAgfCAnb2snXHJcbiAgfCAnaW52YWxpZF9jcmVkZW50aWFscydcclxuICB8ICdpbnZhbGlkX3Bob25lJ1xyXG4gIHwgJ2ludmFsaWRfcGFzc3dvcmQnXHJcbiAgfCAncGhvbmVfZXhpc3RzJ1xyXG4gIHwgJ3VuYXV0aG9yaXplZCdcclxuICB8ICdpbnZhbGlkX3Rva2VuJ1xyXG4gIHwgJ3VzZXJfbm90X2ZvdW5kJ1xyXG4gIHwgJ2ludGVybmFsX2Vycm9yJztcclxuXHJcbi8qKlxyXG4gKiDplJnor6/noIHkuI7nlKjmiLflj4vlpb3mtojmga/nmoTmmKDlsITlr7nosaFcclxuICovXHJcbmV4cG9ydCBjb25zdCBFUlJPUl9NRVNTQUdFUzogUmVjb3JkPEVycm9yQ29kZSwgc3RyaW5nPiA9IHtcclxuICBvazogJ+aTjeS9nOaIkOWKnycsXHJcbiAgaW52YWxpZF9jcmVkZW50aWFsczogJ+i0puWPt+aIluWvhueggemUmeivr++8jOivt+mHjeivlScsXHJcbiAgaW52YWxpZF9waG9uZTogJ+aJi+acuuWPt+agvOW8j+S4jeato+ehricsXHJcbiAgaW52YWxpZF9wYXNzd29yZDogJ+WvhueggeagvOW8j+S4jeato+ehru+8jOWvhueggemVv+W6puW6lOS4ujgtNjTkvY0nLFxyXG4gIHBob25lX2V4aXN0czogJ+ivpeaJi+acuuWPt+W3suiiq+azqOWGjCcsXHJcbiAgdW5hdXRob3JpemVkOiAn6K+35YWI55m75b2V5ZCO5YaN5pON5L2cJyxcclxuICBpbnZhbGlkX3Rva2VuOiAn55m75b2V5bey6L+H5pyf77yM6K+36YeN5paw55m75b2VJyxcclxuICB1c2VyX25vdF9mb3VuZDogJ+eUqOaIt+S4jeWtmOWcqCcsXHJcbiAgaW50ZXJuYWxfZXJyb3I6ICfmnI3liqHlmajlhoXpg6jplJnor6/vvIzor7fnqI3lkI7lho3or5UnXHJcbn07XHJcblxyXG4vKipcclxuICog5qC55o2u6ZSZ6K+v56CB6I635Y+W55So5oi35Y+L5aW955qE6ZSZ6K+v5raI5oGvXHJcbiAqIFxyXG4gKiBAcGFyYW0gY29kZSDplJnor6/noIFcclxuICogQHJldHVybnMg55So5oi35Y+L5aW955qE6ZSZ6K+v5raI5oGvXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZ2V0RXJyb3JNZXNzYWdlKGNvZGU6IHN0cmluZyB8IHVuZGVmaW5lZCk6IHN0cmluZyB7XHJcbiAgaWYgKCFjb2RlKSByZXR1cm4gRVJST1JfTUVTU0FHRVMuaW50ZXJuYWxfZXJyb3I7XHJcbiAgXHJcbiAgcmV0dXJuIChjb2RlIGluIEVSUk9SX01FU1NBR0VTKVxyXG4gICAgPyBFUlJPUl9NRVNTQUdFU1tjb2RlIGFzIEVycm9yQ29kZV1cclxuICAgIDogYOacquefpemUmeivryAoJHtjb2RlfSlgO1xyXG59Il0sIm5hbWVzIjpbIkVSUk9SX01FU1NBR0VTIiwib2siLCJpbnZhbGlkX2NyZWRlbnRpYWxzIiwiaW52YWxpZF9waG9uZSIsImludmFsaWRfcGFzc3dvcmQiLCJwaG9uZV9leGlzdHMiLCJ1bmF1dGhvcml6ZWQiLCJpbnZhbGlkX3Rva2VuIiwidXNlcl9ub3RfZm91bmQiLCJpbnRlcm5hbF9lcnJvciIsImdldEVycm9yTWVzc2FnZSIsImNvZGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/error-messages.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils/validation.ts":
/*!*********************************!*\
  !*** ./lib/utils/validation.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeRole: () => (/* binding */ normalizeRole),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   secureStorage: () => (/* binding */ secureStorage),\n/* harmony export */   validateLoginForm: () => (/* binding */ validateLoginForm),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone),\n/* harmony export */   validateRegisterForm: () => (/* binding */ validateRegisterForm)\n/* harmony export */ });\n// Validation utilities using custom validation functions\n// This avoids dependency on zod for now\nconst validatePhone = (phone)=>{\n    if (!phone) return '手机号不能为空';\n    if (!/^1[3-9]\\d{9}$/.test(phone)) return '请输入有效的手机号';\n    return null;\n};\nconst validatePassword = (password)=>{\n    if (!password) return '密码不能为空';\n    if (password.length < 8) return '密码至少8位';\n    if (password.length > 64) return '密码最多64位';\n    if (/\\s/.test(password)) return '密码不能包含空格';\n    return null;\n};\nconst validateLoginForm = (data)=>{\n    const errors = {};\n    const phoneError = validatePhone(data.phone);\n    if (phoneError) errors.phone = phoneError;\n    const passwordError = validatePassword(data.password);\n    if (passwordError) errors.password = passwordError;\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors\n    };\n};\nconst validateRegisterForm = (data)=>{\n    return validateLoginForm(data);\n};\nconst normalizeRole = (role)=>{\n    if (role === 'admin') return 'ADMIN';\n    if (role === 'user') return 'USER';\n    // Default to USER for unknown roles\n    return 'USER';\n};\nconst sanitizeInput = (input)=>{\n    return input.replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript protocol\n    .trim();\n};\nconst secureStorage = {\n    setItem: (key, value)=>{\n        if (true) {\n            // Use sessionStorage instead of localStorage for sensitive information\n            sessionStorage.setItem(key, value);\n        }\n    },\n    getItem: (key)=>{\n        if (true) {\n            return sessionStorage.getItem(key);\n        }\n        return null;\n    },\n    removeItem: (key)=>{\n        if (true) {\n            sessionStorage.removeItem(key);\n        }\n    },\n    clear: ()=>{\n        if (true) {\n            sessionStorage.clear();\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/validation.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(app-pages-browser)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\.pnpm\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\node_modules\\\\geist\\\\dist\\\\sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\\\"path\\\":\\\"node_modules\\\\\\\\.pnpm\\\\\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\\\\\node_modules\\\\\\\\geist\\\\\\\\dist\\\\\\\\sans.js\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/geist-sans/Geist-Variable.woff2\\\",\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"GeistSans\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\.pnpm\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\node_modules\\\\geist\\\\dist\\\\mono.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-mono/GeistMono-Variable.woff2\",\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"],\"weight\":\"100 900\"}],\"variableName\":\"GeistMono\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\\\"path\\\":\\\"node_modules\\\\\\\\.pnpm\\\\\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\\\\\node_modules\\\\\\\\geist\\\\\\\\dist\\\\\\\\mono.js\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/geist-mono/GeistMono-Variable.woff2\\\",\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"adjustFontFallback\\\":false,\\\"fallback\\\":[\\\"ui-monospace\\\",\\\"SFMono-Regular\\\",\\\"Roboto Mono\\\",\\\"Menlo\\\",\\\"Monaco\\\",\\\"Liberation Mono\\\",\\\"DejaVu Sans Mono\\\",\\\"Courier New\\\",\\\"monospace\\\"],\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"GeistMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*********************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5UUFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxuZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\.pnpm\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\node_modules\\\\geist\\\\dist\\\\mono.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-mono/GeistMono-Variable.woff2\",\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"],\"weight\":\"100 900\"}],\"variableName\":\"GeistMono\"}":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{"path":"node_modules\\.pnpm\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\node_modules\\geist\\dist\\mono.js","import":"","arguments":[{"src":"./fonts/geist-mono/GeistMono-Variable.woff2","variable":"--font-geist-mono","adjustFontFallback":false,"fallback":["ui-monospace","SFMono-Regular","Roboto Mono","Menlo","Monaco","Liberation Mono","DejaVu Sans Mono","Courier New","monospace"],"weight":"100 900"}],"variableName":"GeistMono"} ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace\"},\"className\":\"__className_f910ec\",\"variable\":\"__variable_f910ec\"};\n    if(true) {\n      // 1756974017717\n      var cssReload = __webpack_require__(/*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvbG9jYWwvdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcZ2Vpc3RAMS40LjJfbmV4dEAxNS4yLjRfcmVhX2RhZWY1YjQ5MDQ5NTM0MTVjOGRiM2RhYzA0N2Q1MWIzXFxcXG5vZGVfbW9kdWxlc1xcXFxnZWlzdFxcXFxkaXN0XFxcXG1vbm8uanNcIixcImltcG9ydFwiOlwiXCIsXCJhcmd1bWVudHNcIjpbe1wic3JjXCI6XCIuL2ZvbnRzL2dlaXN0LW1vbm8vR2Vpc3RNb25vLVZhcmlhYmxlLndvZmYyXCIsXCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LW1vbm9cIixcImFkanVzdEZvbnRGYWxsYmFja1wiOmZhbHNlLFwiZmFsbGJhY2tcIjpbXCJ1aS1tb25vc3BhY2VcIixcIlNGTW9uby1SZWd1bGFyXCIsXCJSb2JvdG8gTW9ub1wiLFwiTWVubG9cIixcIk1vbmFjb1wiLFwiTGliZXJhdGlvbiBNb25vXCIsXCJEZWphVnUgU2FucyBNb25vXCIsXCJDb3VyaWVyIE5ld1wiLFwibW9ub3NwYWNlXCJdLFwid2VpZ2h0XCI6XCIxMDAgOTAwXCJ9XSxcInZhcmlhYmxlTmFtZVwiOlwiR2Vpc3RNb25vXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0pBQWdKO0FBQzNLLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLDhWQUFtTSxjQUFjLHNEQUFzRDtBQUNyUyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcbXlfcHJvamVjdFxcd29ya1xcaGVybWVzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxmb250XFxsb2NhbFxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJub2RlX21vZHVsZXNcXC5wbnBtXFxnZWlzdEAxLjQuMl9uZXh0QDE1LjIuNF9yZWFfZGFlZjViNDkwNDk1MzQxNWM4ZGIzZGFjMDQ3ZDUxYjNcXG5vZGVfbW9kdWxlc1xcZ2Vpc3RcXGRpc3RcXG1vbm8uanNcIixcImltcG9ydFwiOlwiXCIsXCJhcmd1bWVudHNcIjpbe1wic3JjXCI6XCIuXFxmb250c1xcZ2Vpc3QtbW9ub1xcR2Vpc3RNb25vLVZhcmlhYmxlLndvZmYyXCIsXCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LW1vbm9cIixcImFkanVzdEZvbnRGYWxsYmFja1wiOmZhbHNlLFwiZmFsbGJhY2tcIjpbXCJ1aS1tb25vc3BhY2VcIixcIlNGTW9uby1SZWd1bGFyXCIsXCJSb2JvdG8gTW9ub1wiLFwiTWVubG9cIixcIk1vbmFjb1wiLFwiTGliZXJhdGlvbiBNb25vXCIsXCJEZWphVnUgU2FucyBNb25vXCIsXCJDb3VyaWVyIE5ld1wiLFwibW9ub3NwYWNlXCJdLFwid2VpZ2h0XCI6XCIxMDAgOTAwXCJ9XSxcInZhcmlhYmxlTmFtZVwiOlwiR2Vpc3RNb25vXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidHZWlzdE1vbm8nLCB1aS1tb25vc3BhY2UsIFNGTW9uby1SZWd1bGFyLCBSb2JvdG8gTW9ubywgTWVubG8sIE1vbmFjbywgTGliZXJhdGlvbiBNb25vLCBEZWphVnUgU2FucyBNb25vLCBDb3VyaWVyIE5ldywgbW9ub3NwYWNlXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9mOTEwZWNcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlX2Y5MTBlY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzU2OTc0MDE3NzE3XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkQ6L215X3Byb2plY3Qvd29yay9oZXJtZXMvZnJvbnRlbmQvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\.pnpm\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\node_modules\\\\geist\\\\dist\\\\mono.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-mono/GeistMono-Variable.woff2\",\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"],\"weight\":\"100 900\"}],\"variableName\":\"GeistMono\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\.pnpm\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\node_modules\\\\geist\\\\dist\\\\sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"}":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{"path":"node_modules\\.pnpm\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\node_modules\\geist\\dist\\sans.js","import":"","arguments":[{"src":"./fonts/geist-sans/Geist-Variable.woff2","variable":"--font-geist-sans","weight":"100 900"}],"variableName":"GeistSans"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'GeistSans', 'GeistSans Fallback'\"},\"className\":\"__className_fb8f2c\",\"variable\":\"__variable_fb8f2c\"};\n    if(true) {\n      // 1756974017716\n      var cssReload = __webpack_require__(/*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\.pnpm\\\\geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3\\\\node_modules\\\\geist\\\\dist\\\\sans.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/geist-sans/Geist-Variable.woff2\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"GeistSans\"}\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);