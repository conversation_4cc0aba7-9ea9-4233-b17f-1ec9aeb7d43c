# 用户管理API接口设计文档

## 1. 概述

基于现有的认证系统和权限控制机制，设计完整的用户管理模块，包括用户列表查询、创建用户、编辑用户、启用/禁用用户等功能。所有接口仅限管理员访问。

## 2. 现有系统分析

### 2.1 用户模型
- 已有 `User` 模型，包含字段：id, user_name, email, phone, password_hash, role, is_active, last_login_at, last_login_ip
- 角色枚举：ADMIN, USER
- 继承自 `TimestampModel`，包含 created_at, updated_at

### 2.2 权限控制
- 已有权限依赖：`require_admin`, `require_user`, `require_roles`
- 用户认证支持：Bearer Token 和 Cookie
- 角色检查机制完善

### 2.3 前端结构
- 已有认证上下文：`AuthContext`
- 侧边栏组件：`Sidebar`
- 用户类型定义：`User`, `AuthContextType`

## 3. API接口设计

### 3.1 用户列表查询

#### 接口定义
```
GET /users
```

#### 请求参数
```typescript
{
  page?: number;        // 页码，默认1
  size?: number;        // 每页大小，默认10
  search?: string;      // 搜索关键词（用户名、邮箱、手机号）
  role?: 'ADMIN' | 'USER';  // 角色过滤
  is_active?: boolean;   // 状态过滤
}
```

#### 响应格式
```typescript
{
  code: number;
  msg: string;
  data: {
    users: User[];
    total: number;
    page: number;
    size: number;
    pages: number;
  };
}
```

#### 权限要求
- 需要管理员权限 (`require_admin`)

### 3.2 创建用户

#### 接口定义
```
POST /users
```

#### 请求体
```typescript
{
  user_name?: string;   // 用户名（可选）
  email?: string;       // 邮箱（可选，但唯一）
  phone?: string;       // 手机号（可选，但唯一）
  password: string;     // 密码（必填）
  role: 'ADMIN' | 'USER';  // 角色（必填）
  is_active?: boolean;  // 是否启用（默认true）
}
```

#### 响应格式
```typescript
{
  code: number;
  msg: string;
  data: User;
}
```

#### 验证规则
- 至少提供 user_name、email、phone 中的一个
- email 和 phone 必须唯一
- password 长度至少6位
- role 必须是有效的枚举值

#### 权限要求
- 需要管理员权限 (`require_admin`)

### 3.3 获取用户详情

#### 接口定义
```
GET /users/{user_id}
```

#### 路径参数
- `user_id`: 用户ID

#### 响应格式
```typescript
{
  code: number;
  msg: string;
  data: User;
}
```

#### 权限要求
- 需要管理员权限 (`require_admin`)

### 3.4 更新用户信息

#### 接口定义
```
PUT /users/{user_id}
```

#### 路径参数
- `user_id`: 用户ID

#### 请求体
```typescript
{
  user_name?: string;   // 用户名
  email?: string;       // 邮箱
  phone?: string;       // 手机号
  role?: 'ADMIN' | 'USER';  // 角色
  is_active?: boolean;  // 是否启用
}
```

#### 响应格式
```typescript
{
  code: number;
  msg: string;
  data: User;
}
```

#### 验证规则
- email 和 phone 必须唯一（如果提供）
- 不能修改自己的角色（防止管理员降级自己）
- role 必须是有效的枚举值

#### 权限要求
- 需要管理员权限 (`require_admin`)

### 3.5 启用/禁用用户

#### 接口定义
```
PATCH /users/{user_id}/status
```

#### 路径参数
- `user_id`: 用户ID

#### 请求体
```typescript
{
  is_active: boolean;  // 是否启用
}
```

#### 响应格式
```typescript
{
  code: number;
  msg: string;
  data: User;
}
```

#### 业务规则
- 不能禁用自己
- 状态变更立即生效

#### 权限要求
- 需要管理员权限 (`require_admin`)

### 3.6 删除用户

#### 接口定义
```
DELETE /users/{user_id}
```

#### 路径参数
- `user_id`: 用户ID

#### 响应格式
```typescript
{
  code: number;
  msg: string;
  data: null;
}
```

#### 业务规则
- 不能删除自己
- 删除操作不可逆
- 软删除或硬删除（根据业务需求）

#### 权限要求
- 需要管理员权限 (`require_admin`)

## 4. 数据模型扩展

### 4.1 用户管理Schema

#### CreateUserSchema
```python
class CreateUserSchema(BaseModel):
    user_name: Optional[str] = Field(None, max_length=255, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    password: str = Field(..., min_length=6, max_length=255, description="密码")
    role: UserRole = Field(..., description="用户角色")
    is_active: bool = Field(True, description="是否启用")
    
    @field_validator('phone')
    def validate_phone(cls, v):
        if v and not v.isdigit():
            raise ValueError("手机号必须为数字")
        return v
    
    @model_validator(mode='after')
    def validate_identifiers(self):
        if not any([self.user_name, self.email, self.phone]):
            raise ValueError("至少需要提供用户名、邮箱或手机号中的一个")
        return self
```

#### UpdateUserSchema
```python
class UpdateUserSchema(BaseModel):
    user_name: Optional[str] = Field(None, max_length=255, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    role: Optional[UserRole] = Field(None, description="用户角色")
    is_active: Optional[bool] = Field(None, description="是否启用")
    
    @field_validator('phone')
    def validate_phone(cls, v):
        if v and not v.isdigit():
            raise ValueError("手机号必须为数字")
        return v
```

#### UserQuerySchema
```python
class UserQuerySchema(BaseModel):
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页大小")
    search: Optional[str] = Field(None, description="搜索关键词")
    role: Optional[UserRole] = Field(None, description="角色过滤")
    is_active: Optional[bool] = Field(None, description="状态过滤")
```

#### UserListResponseSchema
```python
class UserListResponseSchema(BaseModel):
    users: List[UserOut]
    total: int
    page: int
    size: int
    pages: int
```

## 5. 前端组件设计

### 5.1 侧边栏修改
- 在现有侧边栏中添加"用户管理"菜单项
- 仅对管理员用户显示
- 支持折叠状态下的tooltip显示

### 5.2 用户管理页面组件
- 用户列表组件（支持分页、搜索、过滤）
- 用户创建表单组件（包含表单验证）
- 用户编辑表单组件
- 用户状态切换组件

### 5.3 API客户端扩展
- 添加用户管理相关的API方法
- 统一错误处理
- 支持分页和搜索参数

## 6. 实现计划

### 6.1 后端实现步骤
1. 创建用户管理模块目录结构
2. 实现数据模型和验证schema
3. 实现用户管理服务层
4. 实现API路由
5. 在主应用中注册路由

### 6.2 前端实现步骤
1. 扩展类型定义
2. 实现API客户端方法
3. 修改侧边栏组件
4. 创建用户管理页面组件
5. 实现路由和权限控制

## 7. 安全考虑

### 7.1 权限控制
- 所有用户管理接口仅限管理员访问
- 防止管理员修改自己的角色或禁用自己
- 敏感操作需要二次确认

### 7.2 数据验证
- 严格的输入验证
- 防止SQL注入
- 密码强度要求

### 7.3 审计日志
- 记录用户管理操作
- 包含操作人、操作时间、操作内容