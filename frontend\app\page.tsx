'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { LoginForm } from '@/components/auth/login-form'
import { useAuth } from '@/hooks/use-auth'
import { Loader2 } from 'lucide-react'

export default function LoginPage() {
  const router = useRouter()
  const { isAuthenticated, isLoading } = useAuth()

  React.useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, router])

  const handleLoginSuccess = () => {
    router.push('/dashboard')
  }

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  // 如果已认证，显示空白页面（将通过useEffect重定向）
  if (isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative">
      {/* 背景SVG */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <Image 
          src="/icons/login-bg.svg" 
          alt="Background" 
          fill
          priority
          style={{ objectFit: 'cover' }}
        />
      </div>

      <div className="relative z-10">
        <LoginForm onSuccess={handleLoginSuccess} />
      </div>
    </div>
  )
}
