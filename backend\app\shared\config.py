from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    env: str = Field(default="dev", env="HERMES_ENV")

    # 数据库配置
    pg_host: str = Field(default="127.0.0.1", env="PG_HOST")
    pg_port: int = Field(default=5432, env="PG_PORT")
    pg_user: str = Field(default="postgres", env="PG_USER")
    pg_password: str = Field(default="", env="PG_PASSWORD")
    pg_db: str = Field(default="hermes", env="PG_DB")

    # JWT配置
    jwt_secret: str = Field(default="359f7a711e1636df533c693cce8cdcaebf367c63b37e8178bf7602ff735d3fb1", env="JWT_SECRET")
    access_expire_seconds: int = Field(default=600, env="ACCESS_EXPIRE_SECONDS")
    refresh_expire_seconds: int = Field(default=1209600, env="REFRESH_EXPIRE_SECONDS")

    # Cookie配置
    cookie_secure: bool = Field(default=False, env="COOKIE_SECURE")
    cookie_samesite: str = Field(default="Lax", env="COOKIE_SAMESITE")

    # CORS配置
    cors_origins: list[str] = Field(default_factory=lambda: ["http://localhost:3000"])

    # OAuth2配置
    oauth2_token_url: str = Field(default="/auth/token", env="OAUTH2_TOKEN_URL")

    @field_validator("cors_origins", mode="before")
    def parse_cors_origins(cls, v):
        if v is None or v == "":
            return ["http://localhost:3000"]
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",") if origin.strip()]
        if isinstance(v, list):
            return v
        return ["http://localhost:3000"]

    @property
    def database_url(self) -> str:
        """构建数据库连接字符串"""
        return f"postgres://{self.pg_user}:{self.pg_password}@{self.pg_host}:{self.pg_port}/{self.pg_db}"

    model_config = SettingsConfigDict(
        env_file="backend/.env",
        case_sensitive=False,
    )

# 顶层实例化
settings = Settings()

# 提供给 aerich 的静态配置
TORTOISE_ORM = {
    "connections": {"default": settings.database_url},
    "apps": {
        "models": {
            "models": [
                "backend.app.modules.auth.models",
                "aerich.models",
            ],
            "default_connection": "default",
        }
    },
}