import { apiClient } from './client'
import type {
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  User,
  OAuth2TokenResponse,
  OAuth2LoginCredentials,
  BearerTokenAuth
} from '@/lib/types/auth'
import { normalizeRole } from '@/lib/utils/validation'
import { getApiBaseUrl } from '@/lib/utils/api-config'

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials)
    if (response.data) {
      response.data.role = normalizeRole(response.data.role)
    }
    return response
  },

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', credentials)
    if (response.data) {
      response.data.role = normalizeRole(response.data.role)
    }
    return response
  },

  async getCurrentUser(): Promise<{ code: number; msg: string; data: User }> {
    const response = await apiClient.get<{ code: number; msg: string; data: User }>('/auth/me')
    if (response.data) {
      response.data.role = normalizeRole(response.data.role)
    }
    return response
  },

  async logout(): Promise<{ code: number; msg: string; data: null }> {
    const response = await apiClient.post<{ code: number; msg: string; data: null }>('/auth/logout')
    return response
  },

  // OAuth2相关方法
  async loginWithOAuth2(credentials: OAuth2LoginCredentials): Promise<OAuth2TokenResponse> {
    // OAuth2密码流需要使用form data
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const response = await fetch(`${getApiBaseUrl()}/auth/token`, {
      method: 'POST',
      body: formData,
      credentials: 'include', // 保持Cookie兼容性
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `OAuth2 login failed: ${response.status}`)
    }

    const tokenData = await response.json()

    // 标准化用户角色
    if (tokenData.user) {
      tokenData.user.role = normalizeRole(tokenData.user.role)
    }

    return tokenData
  },

  async getCurrentUserWithBearer(token: string): Promise<{ code: number; msg: string; data: User }> {
    const response = await fetch(`${getApiBaseUrl()}/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `Bearer auth failed: ${response.status}`)
    }

    const userData = await response.json()

    // 标准化用户角色
    if (userData.data) {
      userData.data.role = normalizeRole(userData.data.role)
    }

    return userData
  },

  // 混合认证方法（优先Bearer Token，然后Cookie）
  async getCurrentUserFlexible(bearerToken?: string): Promise<{ code: number; msg: string; data: User }> {
    if (bearerToken) {
      try {
        return await this.getCurrentUserWithBearer(bearerToken)
      } catch (error) {
        console.warn('Bearer token auth failed, falling back to cookie auth:', error)
      }
    }

    // 回退到Cookie认证
    return await this.getCurrentUser()
  },
}