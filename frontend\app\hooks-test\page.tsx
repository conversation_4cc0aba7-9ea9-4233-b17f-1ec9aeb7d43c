'use client'

import React, { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'

export default function HooksTestPage() {
  const { user, isAuthenticated, isLoading, logout } = useAuth()
  const [testState, setTestState] = useState('ready')

  const simulateLogout = async () => {
    setTestState('testing')
    try {
      await logout()
      setTestState('success')
    } catch (error) {
      setTestState('error')
      console.error('Logout test error:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🧪 React Hooks 修复测试页面
          </h1>
          
          <div className="space-y-6">
            {/* 修复状态 */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-green-900 mb-3">
                ✅ Hooks 问题修复状态
              </h2>
              <div className="space-y-2 text-sm text-green-700">
                <div>✅ 移除了 auth-status.tsx 中的条件性早期返回</div>
                <div>✅ 修复了 page.tsx 中的条件性早期返回</div>
                <div>✅ 修复了 dashboard/page.tsx 中的条件性早期返回</div>
                <div>✅ 重构了 useAuthenticatedUser hook</div>
              </div>
            </div>

            {/* 当前认证状态 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-blue-900 mb-3">
                🔍 当前认证状态
              </h2>
              <div className="space-y-2 text-sm">
                <div><strong>加载中:</strong> {isLoading ? '是' : '否'}</div>
                <div><strong>已认证:</strong> {isAuthenticated ? '是' : '否'}</div>
                <div><strong>用户:</strong> {user ? user.user_name || user.phone : '无'}</div>
              </div>
            </div>

            {/* 退出登录测试 */}
            {isAuthenticated && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h2 className="text-xl font-semibold text-yellow-900 mb-3">
                  🧪 退出登录测试
                </h2>
                <div className="space-y-4">
                  <p className="text-sm text-yellow-700">
                    点击下面的按钮测试退出登录功能。如果修复成功，应该不会出现 "Rendered fewer hooks than expected" 错误。
                  </p>
                  <button
                    onClick={simulateLogout}
                    disabled={testState === 'testing'}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                  >
                    {testState === 'testing' ? '测试中...' : '测试退出登录'}
                  </button>
                  
                  {testState === 'success' && (
                    <div className="p-3 bg-green-100 border border-green-300 rounded text-green-800">
                      ✅ 退出登录测试成功！没有出现 Hooks 错误。
                    </div>
                  )}
                  
                  {testState === 'error' && (
                    <div className="p-3 bg-red-100 border border-red-300 rounded text-red-800">
                      ❌ 退出登录测试失败。请检查控制台错误信息。
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 问题说明 */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                📋 问题说明
              </h2>
              <div className="space-y-2 text-sm text-gray-700">
                <div><strong>原始错误:</strong> "Rendered fewer hooks than expected"</div>
                <div><strong>原因:</strong> 组件中有条件性的早期返回语句</div>
                <div><strong>影响:</strong> 退出登录时React重新渲染组件，hooks调用顺序不一致</div>
                <div><strong>修复方法:</strong> 用条件渲染替换条件性早期返回</div>
              </div>
            </div>

            {/* 修复详情 */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-purple-900 mb-3">
                🔧 修复详情
              </h2>
              <div className="space-y-3 text-sm text-purple-700">
                <div>
                  <strong>1. AuthStatus 组件:</strong>
                  <div className="ml-4 mt-1">
                    <div>❌ 之前: <code>if (!isAuthenticated) return ...</code></div>
                    <div>✅ 现在: 始终调用所有hooks，用条件渲染内容</div>
                  </div>
                </div>
                
                <div>
                  <strong>2. 页面组件:</strong>
                  <div className="ml-4 mt-1">
                    <div>❌ 之前: <code>if (isLoading) return ...</code></div>
                    <div>✅ 现在: 在return语句内进行条件渲染</div>
                  </div>
                </div>
                
                <div>
                  <strong>3. useAuthenticatedUser Hook:</strong>
                  <div className="ml-4 mt-1">
                    <div>❌ 之前: 条件性返回不同结构</div>
                    <div>✅ 现在: 始终返回相同结构</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 返回链接 */}
            <div className="text-center">
              <a 
                href="/"
                className="inline-block px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                返回首页
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
