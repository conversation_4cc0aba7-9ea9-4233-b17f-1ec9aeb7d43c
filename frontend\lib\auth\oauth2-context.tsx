'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { authService } from '@/lib/api/auth'
import { TokenManager } from './token-manager'
import { getErrorMessage } from '@/lib/utils/error-messages'
import type { 
  User, 
  LoginCredentials, 
  RegisterCredentials, 
  OAuth2LoginCredentials,
  OAuth2TokenResponse 
} from '@/lib/types/auth'

interface OAuth2AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  authMode: 'cookie' | 'bearer' | 'hybrid'
  
  // 传统认证方法
  login: (credentials: LoginCredentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  
  // OAuth2认证方法
  loginWithOAuth2: (credentials: OAuth2LoginCredentials) => Promise<void>
  
  // 通用方法
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
  
  // Bearer Token相关
  getAccessToken: () => string | null
  hasValidToken: () => boolean
}

const OAuth2AuthContext = createContext<OAuth2AuthContextType | undefined>(undefined)

export function OAuth2AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [authMode, setAuthMode] = useState<'cookie' | 'bearer' | 'hybrid'>('hybrid')

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      setIsLoading(true)
      
      // 优先尝试Bearer Token认证
      const accessToken = TokenManager.getAccessToken()
      if (accessToken && !TokenManager.isAccessTokenExpired()) {
        try {
          const response = await authService.getCurrentUserWithBearer(accessToken)
          if (response.code === 0) {
            setUser(response.data)
            setAuthMode('bearer')
            return
          }
        } catch (error) {
          console.warn('Bearer token auth failed, trying cookie auth:', error)
          TokenManager.clearTokens()
        }
      }
      
      // 回退到Cookie认证
      try {
        const response = await authService.getCurrentUser()
        if (response.code === 0) {
          setUser(response.data)
          setAuthMode('cookie')
        }
      } catch (error) {
        console.log('Cookie auth also failed, user not authenticated:', error)
        setUser(null)
        setAuthMode('hybrid')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setUser(null)
      setAuthMode('hybrid')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true)
    try {
      const response = await authService.login(credentials)
      if (response.code === 0) {
        setUser(response.data)
        setAuthMode('cookie')
      } else {
        throw new Error(getErrorMessage(response.msg) || '登录失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const loginWithOAuth2 = async (credentials: OAuth2LoginCredentials) => {
    setIsLoading(true)
    try {
      const tokenResponse: OAuth2TokenResponse = await authService.loginWithOAuth2(credentials)
      
      // 存储令牌
      TokenManager.storeTokens({
        access_token: tokenResponse.access_token,
        refresh_token: tokenResponse.refresh_token,
        token_type: tokenResponse.token_type,
        expires_in: tokenResponse.expires_in
      })
      
      // 设置用户信息
      setUser(tokenResponse.user)
      setAuthMode('bearer')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'OAuth2登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true)
    try {
      const response = await authService.register(credentials)
      if (response.code === 0) {
        setUser(response.data)
        setAuthMode('cookie')
      } else {
        throw new Error(getErrorMessage(response.msg) || '注册失败')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除所有认证状态
      TokenManager.clearTokens()
      setUser(null)
      setAuthMode('hybrid')
    }
  }

  const refreshUser = async () => {
    await checkAuth()
  }

  const getAccessToken = () => {
    return TokenManager.getAccessToken()
  }

  const hasValidToken = () => {
    return TokenManager.hasValidAccessToken()
  }

  const value: OAuth2AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    authMode,
    login,
    loginWithOAuth2,
    register,
    logout,
    refreshUser,
    getAccessToken,
    hasValidToken,
  }

  return (
    <OAuth2AuthContext.Provider value={value}>
      {children}
    </OAuth2AuthContext.Provider>
  )
}

export function useOAuth2Auth() {
  const context = useContext(OAuth2AuthContext)
  if (context === undefined) {
    throw new Error('useOAuth2Auth must be used within an OAuth2AuthProvider')
  }
  return context
}
