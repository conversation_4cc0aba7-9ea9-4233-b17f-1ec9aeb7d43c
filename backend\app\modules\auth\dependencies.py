"""
认证依赖模块
提供统一的认证和授权依赖函数
"""
from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, status, Request, Security
from fastapi.security import HTTPAuthorizationCredentials
from backend.app.shared.security import (
    bearer_scheme,
    verify_token_and_get_payload,
    verify_bearer_token_and_get_payload,
    verify_flexible_token_and_get_payload,
    get_token_from_request
)
from backend.app.modules.auth.models import User, UserRole

async def _validate_and_get_user(user_id: int) -> Dict[str, Any]:
    """
    通用的用户验证和获取逻辑
    
    Args:
        user_id: 用户ID
        
    Returns:
        Dict[str, Any]: 用户信息
        
    Raises:
        HTTPException: 用户不存在或未激活时抛出异常
    """
    user = await User.filter(id=user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return {
        "id": user.id,
        "phone": user.phone,
        "email": user.email,
        "user_name": user.user_name,
        "role": user.role,
        "is_active": user.is_active,
    }

def _get_role_value(user_role: Any) -> str:
    """
    获取角色值的通用函数
    
    Args:
        user_role: 用户角色（可能是枚举或字符串）
        
    Returns:
        str: 角色值
    """
    return user_role.value if hasattr(user_role, "value") else str(user_role)

async def get_current_user(
    request: Request = None,
    credentials: HTTPAuthorizationCredentials = Security(bearer_scheme)
) -> Dict[str, Any]:
    """
    获取当前用户（统一认证方式，支持Bearer Token和Cookie认证）
    
    优先使用Bearer Token认证，如果没有Bearer Token则使用Cookie认证。
    
    Args:
        request: FastAPI请求对象（用于Cookie认证）
        credentials: HTTP Authorization credentials（用于Bearer Token认证）
        
    Returns:
        Dict[str, Any]: 当前用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    # 优先尝试Bearer Token认证
    if credentials:
        try:
            payload = await verify_bearer_token_and_get_payload(credentials)
            user_id = int(payload.get("sub"))
            return await _validate_and_get_user(user_id)
        except HTTPException:
            # Bearer Token认证失败，继续尝试Cookie认证
            pass
    
    # 回退到Cookie认证
    if request:
        try:
            payload = await verify_flexible_token_and_get_payload(request)
            user_id = int(payload.get("sub"))
            return await _validate_and_get_user(user_id)
        except HTTPException:
            pass
    
    # 两种认证方式都失败
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Authentication failed",
        headers={"WWW-Authenticate": "Bearer"},
    )

async def get_current_user_bearer(
    credentials: HTTPAuthorizationCredentials = Security(bearer_scheme)
) -> Dict[str, Any]:
    """
    仅通过Bearer Token获取当前用户（保持向后兼容）
    
    Args:
        credentials: HTTP Authorization credentials
        
    Returns:
        Dict[str, Any]: 当前用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    try:
        payload = await verify_bearer_token_and_get_payload(credentials)
        user_id = int(payload.get("sub"))
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token validation failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return await _validate_and_get_user(user_id)

def require_roles(*allowed_roles: UserRole):
    """
    角色权限检查依赖工厂函数
    
    Args:
        *allowed_roles: 允许的用户角色列表
        
    Returns:
        function: 依赖函数
    """
    async def role_checker(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        user_role = _get_role_value(current_user.get("role"))
        allowed_role_values = [_get_role_value(role) for role in allowed_roles]
        
        if user_role not in allowed_role_values:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        
        return current_user
    
    return role_checker

async def _check_admin_permission(current_user: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查管理员权限的通用函数
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        Dict[str, Any]: 当前管理员用户信息
        
    Raises:
        HTTPException: 权限不足时抛出403错误
    """
    user_role = _get_role_value(current_user.get("role"))
    
    if user_role != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return current_user

async def require_admin(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    管理员权限检查依赖
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        Dict[str, Any]: 当前管理员用户信息
        
    Raises:
        HTTPException: 权限不足时抛出403错误
    """
    return await _check_admin_permission(current_user)

async def require_admin_bearer(
    current_user: Dict[str, Any] = Depends(get_current_user_bearer)
) -> Dict[str, Any]:
    """
    管理员权限检查依赖（仅Bearer认证）
    使用 Bearer 安全方案，便于在 Swagger 显示"小锁"
    """
    return await _check_admin_permission(current_user)

async def require_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    普通用户权限检查依赖（包括管理员）
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        Dict[str, Any]: 当前用户信息
    """
    # 任何活跃用户都可以访问
    return current_user

# 便捷的角色检查依赖
require_admin_role = require_roles(UserRole.ADMIN)
require_user_role = require_roles(UserRole.USER, UserRole.ADMIN)