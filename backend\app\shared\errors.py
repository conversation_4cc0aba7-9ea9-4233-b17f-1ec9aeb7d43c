from enum import Enum
from typing import Dict, Any, Optional, Tuple


class ErrorCode(str, Enum):
    """统一的错误码枚举
    
    用于统一后端API响应中的错误消息，替代之前混合使用的中英文错误消息。
    前端可以基于这些枚举值做本地化和可读性映射。
    """
    OK = "ok"
    INVALID_CREDENTIALS = "invalid_credentials"  # 凭证无效（如用户名/密码错误）
    INVALID_PHONE = "invalid_phone"  # 手机号格式无效
    INVALID_PASSWORD = "invalid_password"  # 密码格式无效
    PHONE_EXISTS = "phone_exists"  # 手机号已存在
    UNAUTHORIZED = "unauthorized"  # 未授权或未登录
    INVALID_TOKEN = "invalid_token"  # 无效的令牌
    USER_NOT_FOUND = "user_not_found"  # 用户不存在
    INTERNAL_ERROR = "internal_error"  # 服务器内部错误


class AppError(Exception):
    """自定义应用异常
    
    用于统一处理应用层的错误，携带ErrorCode和HTTP状态码，
    避免在路由层重复进行错误转换逻辑。
    """
    
    def __init__(
        self,
        error_code: ErrorCode,
        data: Any = None,
        http_status: Optional[int] = None
    ):
        """
        初始化应用异常
        
        Args:
            error_code: 错误码枚举值
            data: 可选的额外数据
            http_status: 可选的HTTP状态码，如果未提供则从ErrorCode映射
        """
        self.error_code = error_code
        self.data = data
        
        # 如果未提供http_status，则从ErrorCode映射
        if http_status is None:
            self.http_status = ERROR_CODE_TO_HTTP_STATUS.get(error_code, 500)
        else:
            self.http_status = http_status
            
        # 调用父类构造函数，传递错误消息
        super().__init__(error_code.value)


def error_response(code: int, msg: str, data: Any = None) -> Dict[str, Any]:
    """创建标准格式的错误响应
    
    Args:
        code: HTTP状态码或自定义错误码
        msg: 错误消息，应使用ErrorCode枚举值
        data: 可选的额外数据
        
    Returns:
        包含code、msg和data的标准格式响应字典
    """
    return {
        "code": code,
        "msg": msg,
        "data": data
    }


def success_response(data: Any = None) -> Dict[str, Any]:
    """创建标准格式的成功响应
    
    Args:
        data: 响应数据
        
    Returns:
        包含code、msg和data的标准格式响应字典
    """
    return {
        "code": 0,
        "msg": ErrorCode.OK,
        "data": data
    }


# 错误码与HTTP状态码的映射关系
ERROR_CODE_TO_HTTP_STATUS = {
    ErrorCode.INVALID_CREDENTIALS: 401,
    ErrorCode.INVALID_PHONE: 400,
    ErrorCode.INVALID_PASSWORD: 400,
    ErrorCode.PHONE_EXISTS: 409,  # Conflict
    ErrorCode.UNAUTHORIZED: 401,
    ErrorCode.INVALID_TOKEN: 401,
    ErrorCode.USER_NOT_FOUND: 404,
    ErrorCode.INTERNAL_ERROR: 500
}


def get_error_details(error_code: ErrorCode) -> Tuple[int, str]:
    """获取错误码对应的HTTP状态码和错误消息

    Args:
        error_code: 错误码枚举值

    Returns:
        (HTTP状态码, 错误消息)元组
    """
    return ERROR_CODE_TO_HTTP_STATUS.get(error_code, 500), error_code


def handle_service_error(e: Exception, response) -> Dict[str, Any]:
    """统一处理服务层异常的工具函数

    Args:
        e: 捕获的异常
        response: FastAPI Response对象，用于设置状态码

    Returns:
        标准格式的错误响应字典
    """
    if isinstance(e, ValueError):
        # 统一从 ValueError 中解析 ErrorCode
        err = e.args[0] if e.args else ErrorCode.INTERNAL_ERROR
        if isinstance(err, ErrorCode):
            http_status, error_msg = get_error_details(err)
            response.status_code = http_status
            return error_response(http_status, error_msg)
        elif isinstance(err, str) and err in [ec.value for ec in ErrorCode]:
            http_status, error_msg = get_error_details(ErrorCode(err))
            response.status_code = http_status
            return error_response(http_status, error_msg)
        # 兼容其他错误
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)
    else:
        # 处理其他异常
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)