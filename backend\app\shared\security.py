from typing import Any, Optional
import time
import uuid
from fastapi import Response, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JWTError
from jose.exceptions import ExpiredSignatureError
from passlib.context import Crypt<PERSON>ontext
from backend.app.shared.config import settings

ALGORITHM = "HS256"
COOKIE_ACCESS = "cookie_access"
COOKIE_REFRESH = "cookie_refresh"

# Password hash encryption
__pwd_ctx = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Bearer Token认证
bearer_scheme = HTTPBearer(auto_error=False)

def hash_password(plain: str) -> str:
    """
    Password hash encryption
    """
    return __pwd_ctx.hash(plain)

def verify_password(plain: str, hashed: str) -> bool:
    """
    Password hash verification
    """
    return __pwd_ctx.verify(plain, hashed)

def _create_token(*, typ: str, user_id: int, expires_in_seconds: int) -> str:
    """
    Create JWT token
    """
    iat = int(time.time())
    exp = iat + int(expires_in_seconds)
    payload = {
        "sub": str(user_id),  # JWT标准要求sub必须是字符串
        "typ": typ,
        "jti": str(uuid.uuid4()),
        "iat": iat,
        "exp": exp,
    }
    return jwt.encode(payload, settings.jwt_secret, algorithm=ALGORITHM)

def create_access_token(user_id: int) -> str:
    """
    Create access token
    """
    return _create_token(typ="access", user_id=user_id, expires_in_seconds=settings.access_expire_seconds)

def create_refresh_token(user_id: int) -> str:
    """
    Create refresh token
    """
    return _create_token(typ="refresh", user_id=user_id, expires_in_seconds=settings.refresh_expire_seconds)

def verify_token(token: str, expected_type: str) -> dict[str, Any]:
    """
    Verify token
    Args:
        token: JWT token string
        expected_type: Expected token type ('access' or 'refresh')
    Returns:
        dict: Token payload if valid
    Raises:
        ValueError: If token is invalid or expired
    """
    try:
        payload = jwt.decode(token, settings.jwt_secret, algorithms=[ALGORITHM])
    except ExpiredSignatureError:
        raise ValueError("token_expired")
    except JWTError :
        raise ValueError("invalid_token")

    if payload.get("typ") != expected_type:
        raise ValueError("invalid_token_type")

    return payload

def set_auth_cookies(response: Response, access_token: str, refresh_token: str) -> None:
    """
    Set authentication cookies in response
    Args:
        response: FastAPI response object
        access_token: JWT access token
        refresh_token: JWT refresh token
    """
    samesite = (settings.cookie_samesite or "Lax").lower()
    response.set_cookie(
        key=COOKIE_ACCESS,
        value=access_token,
        httponly=True,
        samesite=samesite,
        secure=bool(settings.cookie_secure),
        max_age=int(settings.access_expire_seconds),
        path="/",
    )
    response.set_cookie(
        key=COOKIE_REFRESH,
        value=refresh_token,
        httponly=True,
        samesite=samesite,
        secure=bool(settings.cookie_secure),
        max_age=int(settings.refresh_expire_seconds),
        path="/",
    )

def set_access_cookie(response: Response, access_token: str) -> None:
    """
    Set only the access token cookie (do not touch refresh cookie)
    Args:
        response: FastAPI response object
        access_token: JWT access token
    """
    samesite = (settings.cookie_samesite or "Lax").lower()
    response.set_cookie(
        key=COOKIE_ACCESS,
        value=access_token,
        httponly=True,
        samesite=samesite,
        secure=bool(settings.cookie_secure),
        max_age=int(settings.access_expire_seconds),
        path="/",
    )

def clear_auth_cookies(response: Response) -> None:
    """
    Clear authentication cookies from response
    Args:
        response: FastAPI response object
    """
    response.delete_cookie(COOKIE_ACCESS, path="/")
    response.delete_cookie(COOKIE_REFRESH, path="/")

async def verify_token_and_get_payload(token: str, expected_type: str) -> dict[str, Any]:
    """
    验证令牌并返回载荷（不查询用户）
    
    Args:
        token: JWT token string
        expected_type: Expected token type ('access' or 'refresh')
    
    Returns:
        dict: Token payload if valid
    
    Raises:
        ValueError: If token is invalid or expired
    """
    try:
        payload = verify_token(token, expected_type)
        user_id = int(payload.get("sub"))
        if user_id is None:
            raise ValueError("Invalid token payload")
        return payload
    except ValueError as e:
        raise ValueError(f"Token validation failed: {str(e)}")

async def verify_bearer_token_and_get_payload(credentials: Optional[HTTPAuthorizationCredentials] = None) -> dict[str, Any]:
    """
    从Bearer Token验证令牌并返回载荷（用于依赖注入）

    Args:
        credentials: HTTP Authorization credentials

    Returns:
        dict[str, Any]: 令牌载荷字典

    Raises:
        HTTPException: Token无效时抛出401错误
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing Bearer token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        return await verify_token_and_get_payload(credentials.credentials, "access")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token validation failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_token_from_request(request: Request) -> Optional[str]:
    """
    从请求中提取token（优先Bearer Token，然后Cookie）

    Args:
        request: FastAPI请求对象

    Returns:
        Optional[str]: 提取的token，如果没有找到返回None
    """
    # 优先检查Authorization头中的Bearer Token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        return auth_header[7:]  # 移除"Bearer "前缀

    # 如果没有Bearer Token，检查Cookie
    return request.cookies.get(COOKIE_ACCESS)

async def verify_flexible_token_and_get_payload(request: Request) -> dict[str, Any]:
    """
    灵活的令牌验证（支持Bearer Token和Cookie）

    Args:
        request: FastAPI请求对象

    Returns:
        dict[str, Any]: 令牌载荷字典

    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    token = await get_token_from_request(request)

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No authentication token provided",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        return await verify_token_and_get_payload(token, "access")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token validation failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

