# 认证流程优化方案

## 问题分析

### 当前问题
1. **认证方式混乱**：系统同时存在 Cookie 认证、Bearer Token 认证和 OAuth2 认证
2. **令牌刷新缺陷**：前端刷新令牌后没有正确更新本地存储的访问令牌
3. **重复的认证逻辑**：前端有多个认证上下文（AuthContext 和 OAuth2AuthContext）
4. **后端认证依赖混乱**：存在多种认证依赖函数，功能重叠
5. **令牌管理不一致**：Cookie 和 Bearer Token 的处理方式不统一

### 根本原因
- 前端在刷新令牌后，没有更新本地存储的访问令牌
- 多种认证方式并存，导致逻辑复杂和冲突
- 令牌刷新机制不完善，没有正确处理刷新后的状态同步

## 优化目标

1. **统一认证方式**：采用混合认证模式，优先使用 Bearer Token，回退到 Cookie
2. **简化令牌管理**：统一令牌的存储、刷新和验证逻辑
3. **优化刷新机制**：确保令牌刷新后正确更新本地状态
4. **消除重复代码**：合并重复的认证逻辑和上下文
5. **提高类型安全**：完善 TypeScript 类型定义

## 架构设计

### 新的认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库

    U->>F: 登录请求
    F->>B: POST /auth/login (或 /auth/token)
    B->>DB: 验证用户凭据
    DB-->>B: 返回用户信息
    B->>B: 生成访问令牌和刷新令牌
    B-->>F: 返回令牌和用户信息
    F->>F: 存储令牌到 localStorage 和 Cookie
    F->>U: 显示登录成功

    Note over F,B: 令牌刷新流程
    F->>B: 请求受保护资源
    B-->>F: 401 Unauthorized
    F->>F: 检测到 401，自动刷新令牌
    F->>B: POST /auth/refresh (使用刷新令牌)
    B->>DB: 验证刷新令牌
    DB-->>B: 验证成功
    B->>B: 生成新的访问令牌
    B-->>F: 返回新的访问令牌
    F->>F: 更新本地存储的访问令牌
    F->>B: 重试原始请求
    B-->>F: 返回请求结果
```

### 认证方式优先级

1. **Bearer Token**（优先）
   - 从 localStorage 获取
   - 通过 Authorization 头发送
   - 适用于 API 请求

2. **Cookie**（回退）
   - 从 HttpOnly Cookie 获取
   - 自动随请求发送
   - 适用于浏览器环境

## 实施计划

### 1. 后端优化

#### 1.1 简化认证依赖
- 合并 `get_current_user` 和 `get_current_user_bearer` 函数
- 统一令牌验证逻辑
- 优化错误处理

#### 1.2 优化令牌刷新
- 确保 `/auth/refresh` 端点正确设置新的访问令牌 Cookie
- 统一响应格式
- 改进错误处理

#### 1.3 统一认证方式
- 修改 `/auth/me` 端点支持灵活认证
- 确保所有认证方式使用相同的用户验证逻辑

### 2. 前端优化

#### 2.1 统一认证上下文
- 合并 `AuthContext` 和 `OAuth2AuthContext`
- 创建统一的认证状态管理
- 简化认证 API

#### 2.2 重构令牌管理器
- 改进 `TokenManager` 类
- 支持混合认证模式
- 优化令牌存储和获取逻辑

#### 2.3 优化 API 客户端
- 改进令牌刷新机制
- 确保刷新后更新本地令牌
- 优化重试逻辑

#### 2.4 更新类型定义
- 统一认证相关的 TypeScript 类型
- 确保类型安全
- 简化类型定义

### 3. 组件和表单优化

#### 3.1 统一认证组件
- 合并登录表单
- 统一认证状态显示
- 简化认证流程

#### 3.2 优化用户体验
- 改进错误提示
- 优化加载状态
- 简化认证流程

## 预期效果

1. **认证流程简化**：统一的认证方式，减少复杂性
2. **令牌管理优化**：正确的令牌刷新和状态同步
3. **代码质量提升**：消除重复代码，提高可维护性
4. **用户体验改善**：更流畅的认证流程，减少错误
5. **类型安全增强**：完善的 TypeScript 类型定义

## 风险评估

### 低风险
- 重构认证上下文
- 优化令牌管理器
- 更新类型定义

### 中等风险
- 修改后端认证依赖
- 优化令牌刷新机制
- 统一认证方式

### 高风险
- 大规模重构认证流程
- 修改核心认证逻辑

## 测试计划

1. **单元测试**：测试各个认证组件和函数
2. **集成测试**：测试完整的认证流程
3. **端到端测试**：测试用户登录、令牌刷新和访问受保护资源
4. **兼容性测试**：确保新旧认证方式的兼容性

## 实施时间表

- **第1周**：分析现有代码，设计新架构
- **第2周**：重构后端认证逻辑
- **第3周**：重构前端认证上下文和令牌管理
- **第4周**：优化 API 客户端和组件
- **第5周**：测试和文档编写

## 总结

本优化方案旨在解决当前认证系统中的各种问题，通过统一认证方式、简化令牌管理、优化刷新机制等措施，提高系统的可维护性、可靠性和用户体验。实施过程中需要谨慎处理，确保不影响现有功能，并充分测试各种场景。