from fastapi import APIRouter, HTTPException, Response, Request, Depends, Security
from fastapi.security import OAuth2PasswordRequestForm
from backend.app.modules.auth.schemas import RegisterIn, LoginIn, TokenResponse
from backend.app.modules.auth.services.auth import register_with_phone, login_with_phone
from backend.app.modules.auth.services.oauth2 import oauth2_login
from backend.app.shared.security import set_auth_cookies, clear_auth_cookies, verify_token, create_access_token, COOKIE_REFRESH, set_access_cookie
from backend.app.modules.auth.dependencies import get_current_user, require_admin_bearer, get_current_user_bearer
from backend.app.shared.errors import ErrorCode, success_response, error_response, handle_service_error
from backend.app.shared.config import settings

router = APIRouter()

@router.post(
    "/register",
    summary="用户注册",
    description="创建新用户账户，需要管理员权限。注册成功后会自动设置认证Cookie。",
    responses={
        200: {
            "description": "注册成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "success",
                        "data": {
                            "id": 1,
                            "phone": "13800138000",
                            "user_name": "user1",
                            "role": "USER",
                            "is_active": True,
                            "tokens": {
                                "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                                "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "invalid_phone",
                        "data": None
                    }
                }
            }
        },
        401: {
            "description": "未授权访问",
            "content": {
                "application/json": {
                    "example": {
                        "code": 401,
                        "msg": "unauthorized",
                        "data": None
                    }
                }
            }
        },
        409: {
            "description": "手机号已存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 409,
                        "msg": "phone_exists",
                        "data": None
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 500,
                        "msg": "internal_error",
                        "data": None
                    }
                }
            }
        }
    },

)
async def register(payload: RegisterIn,
                   response: Response,
                   current_user: dict = Security(require_admin_bearer)
                   ):
    """
    用户注册接口
    
    创建新用户账户，需要管理员权限。注册成功后会自动设置认证Cookie。
    
    Args:
        payload: 注册请求参数，包含手机号、密码和角色信息
        response: HTTP响应对象，用于设置Cookie
        current_user: 当前用户信息（需要管理员权限）
    
    Returns:
        dict: 注册成功的用户信息，包含用户ID、手机号、用户名、角色、状态和认证令牌
    
    Raises:
        HTTPException: 当认证失败、参数错误或服务器内部错误时抛出
    """
    try:
        user = await register_with_phone(payload.phone, payload.password, payload.role)
        role = user["role"]
        user['role'] = role.value if hasattr(role, "value") else str(role)
        #设置cookie
        set_auth_cookies(response, user["access_token"], user["refresh_token"])
        
        return success_response({
            "id": user["id"],
            "phone": user["phone"],
            "user_name": user["user_name"],
            "role": user["role"],
            "is_active": user["is_active"],
            "tokens": {
                "access": user["access_token"],
                "refresh": user["refresh_token"]
            }
        })
    
    except Exception as e:
        return handle_service_error(e, response)

@router.post(
    "/login",
    summary="用户登录",
    description="使用手机号和密码进行用户登录，登录成功后会自动设置认证Cookie。",
    responses={
        200: {
            "description": "登录成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "success",
                        "data": {
                            "id": 1,
                            "phone": "13800138000",
                            "user_name": "user1",
                            "role": "USER",
                            "is_active": True,
                            "tokens": {
                                "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                                "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "invalid_phone",
                        "data": None
                    }
                }
            }
        },
        401: {
            "description": "认证失败",
            "content": {
                "application/json": {
                    "example": {
                        "code": 401,
                        "msg": "invalid_credentials",
                        "data": None
                    }
                }
            }
        },
        404: {
            "description": "用户不存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 404,
                        "msg": "user_not_found",
                        "data": None
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 500,
                        "msg": "internal_error",
                        "data": None
                    }
                }
            }
        }
    },

)
async def login(payload: LoginIn, response: Response):
    """
    用户登录接口
    
    使用手机号和密码进行用户登录，登录成功后会自动设置认证Cookie。
    
    Args:
        payload: 登录请求参数，包含手机号和密码
        response: HTTP响应对象，用于设置Cookie
    
    Returns:
        dict: 登录成功的用户信息，包含用户ID、手机号、用户名、角色、状态和认证令牌
    
    Raises:
        HTTPException: 当认证失败、参数错误或服务器内部错误时抛出
    """
    try:
        user = await login_with_phone(payload.phone, payload.password)
        role = user["role"]
        user['role'] = role.value if hasattr(role, "value") else str(role)
        
        # 设置Cookie
        set_auth_cookies(response, user["access_token"], user["refresh_token"])
        
        return success_response({
            "id": user["id"],
            "phone": user["phone"],
            "user_name": user["user_name"],
            "role": user["role"],
            "is_active": user["is_active"],
            "tokens": {
                "access": user["access_token"],
                "refresh": user["refresh_token"]
            }
        })
    except Exception as e:
        return handle_service_error(e, response)


@router.post(
    "/refresh",
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌。通过HttpOnly cookie中的刷新令牌来验证用户身份，并返回新的访问令牌。",
    responses={
        200: {
            "description": "刷新成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "success",
                        "data": None
                    }
                }
            }
        },
        401: {
            "description": "未授权或令牌无效",
            "content": {
                "application/json": {
                    "examples": {
                        "unauthorized": {
                            "summary": "未授权",
                            "value": {
                                "code": 401,
                                "msg": "unauthorized",
                                "data": None
                            }
                        },
                        "invalid_token": {
                            "summary": "令牌无效",
                            "value": {
                                "code": 401,
                                "msg": "invalid_token",
                                "data": None
                            }
                        }
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 500,
                        "msg": "internal_error",
                        "data": None
                    }
                }
            }
        }
    },

)
async def refresh(request: Request, response: Response):
    """
    刷新访问令牌
    
    使用刷新令牌获取新的访问令牌。通过HttpOnly cookie中的刷新令牌来验证用户身份，并返回新的访问令牌。
    
    Args:
        request: HTTP请求对象，用于获取Cookie中的刷新令牌
        response: HTTP响应对象，用于设置新的访问令牌Cookie
    
    Returns:
        dict: 刷新成功的响应消息，包含新的访问令牌信息
    
    Raises:
        HTTPException: 当令牌无效、用户不存在或服务器内部错误时抛出
    """
    try:
        refresh_token = request.cookies.get(COOKIE_REFRESH)
        if not refresh_token:
            return error_response(401, ErrorCode.UNAUTHORIZED)
        
        # 验证刷新令牌
        try:
            payload = verify_token(refresh_token, "refresh")
            user_id = int(payload.get("sub"))  # 转换为整数
        except ValueError:
            return error_response(401, ErrorCode.INVALID_TOKEN)
        
        # 验证用户存在且活跃
        try:
            # 延迟导入，避免循环依赖
            from backend.app.modules.auth.models import User
            user = await User.filter(id=user_id).first()
            if not user or not user.is_active:
                return error_response(401, ErrorCode.UNAUTHORIZED)
        except Exception:
            # 即便用户查询异常，也避免泄露细节
            response.status_code = 500
            return error_response(500, ErrorCode.INTERNAL_ERROR)
        
        # 签发新的访问令牌
        new_access = create_access_token(user_id)
        
        # 设置新的访问令牌Cookie
        set_access_cookie(response, new_access)
        
        # 返回新的访问令牌信息，便于前端更新本地存储
        return success_response({
            "access_token": new_access,
            "token_type": "bearer",
            "expires_in": settings.access_expire_seconds
        })
    except Exception:
        response.status_code = 500
        return error_response(500, ErrorCode.INTERNAL_ERROR)

@router.post(
    "/logout",
    summary="用户登出",
    description="清除用户的认证Cookie，使用户登出系统。",
    responses={
        200: {
            "description": "登出成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "success",
                        "data": None
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 500,
                        "msg": "internal_error",
                        "data": None
                    }
                }
            }
        }
    },

)
async def logout(response: Response):
    """
    用户登出接口
    
    清除用户的认证Cookie，使用户登出系统。
    
    Args:
        response: HTTP响应对象，用于清除Cookie
    
    Returns:
        dict: 登出成功的响应消息
    
    Raises:
        HTTPException: 当服务器内部错误时抛出
    """
    # 清除Cookie
    clear_auth_cookies(response)

    return success_response(None)

# OAuth2标准端点
@router.post(
    "/token",
    summary="OAuth2令牌获取",
    description="OAuth2密码流标准令牌端点，用于获取访问令牌和刷新令牌。",
    response_model=TokenResponse,
    responses={
        200: {
            "description": "令牌获取成功",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "token_type": "bearer",
                        "expires_in": 600,
                        "user": {
                            "id": 1,
                            "phone": "13800138000",
                            "user_name": "user1",
                            "role": "USER",
                            "is_active": True
                        }
                    }
                }
            }
        },
        401: {
            "description": "认证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Incorrect username or password"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error: ..."
                    }
                }
            }
        }
    },

)
async def oauth2_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    OAuth2密码流令牌端点
    
    OAuth2密码流标准令牌端点，用于获取访问令牌和刷新令牌。
    
    Args:
        form_data: OAuth2密码表单数据，包含用户名和密码
    
    Returns:
        TokenResponse: OAuth2标准令牌响应，包含访问令牌、刷新令牌、令牌类型、过期时间和用户信息
    
    Raises:
        HTTPException: 当认证失败或服务器内部错误时抛出
    """
    try:
        token_data = await oauth2_login(form_data)
        return token_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get(
    "/me",
    summary="获取当前用户信息",
    description="获取当前登录用户的详细信息，支持Bearer Token和Cookie两种认证方式。",
    responses={
        200: {
            "description": "获取成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "success",
                        "data": {
                            "id": 1,
                            "phone": "13800138000",
                            "user_name": "user1",
                            "role": "USER",
                            "is_active": True
                        }
                    }
                }
            }
        },
        401: {
            "description": "未授权访问",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Not authenticated"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Failed to get user info: ..."
                    }
                }
            }
        }
    },

)
async def get_current_user_info(current_user: dict = Security(get_current_user_bearer)):
    """
    获取当前用户信息
    
    获取当前登录用户的详细信息，支持Bearer Token和Cookie两种认证方式。
    
    Args:
        current_user: 当前用户信息，通过认证依赖注入获取
    
    Returns:
        dict: 当前用户的详细信息，包含用户ID、手机号、用户名、角色和状态
    
    Raises:
        HTTPException: 当未认证或服务器内部错误时抛出
    """
    try:
        role = current_user["role"]
        current_user['role'] = role.value if hasattr(role, "value") else str(role)

        return success_response({
            "id": current_user["id"],
            "phone": current_user["phone"],
            "user_name": current_user["user_name"],
            "role": current_user["role"],
            "is_active": current_user["is_active"],
        })
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user info: {str(e)}"
        )
